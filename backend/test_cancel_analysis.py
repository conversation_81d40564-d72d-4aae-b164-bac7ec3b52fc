#!/usr/bin/env python3
"""
Script de test pour la fonctionnalité d'annulation d'analyses
"""

from app.extensions import mongo
from app import create_app
from app.models.analysis import AnalysisManager
from bson import ObjectId
from datetime import datetime, timezone

def test_cancel_analysis():
    """Teste la fonctionnalité d'annulation d'analyse"""
    print("🧪 Test de la fonctionnalité d'annulation d'analyse...")
    
    app = create_app()
    with app.app_context():
        # Trouver un utilisateur admin
        admin_user = mongo.db.users.find_one({'role': 'admin'})
        if not admin_user:
            print("❌ Aucun utilisateur admin trouvé")
            return
        
        user_id = str(admin_user['_id'])
        print(f"👤 Utilisateur de test: {admin_user['username']} ({user_id})")
        
        # Créer une analyse de test
        test_analysis_id = "test-cancel-analysis-123"
        
        try:
            # Créer l'analyse
            mongo_id = AnalysisManager.create_analysis(
                user_id=user_id,
                analysis_id=test_analysis_id,
                file_info={
                    'filename': 'test_cancel.csv',
                    'size': 1024,
                    'content_type': 'text/csv'
                },
                analysis_type='full'
            )
            print(f"✅ Analyse de test créée: {test_analysis_id} (MongoDB: {mongo_id})")
            
            # Mettre l'analyse en statut "running"
            AnalysisManager.update_analysis_status(
                analysis_id=test_analysis_id,
                status='running',
                progress=50
            )
            print(f"✅ Analyse mise en statut 'running'")
            
            # Tester l'annulation
            cancelled = AnalysisManager.cancel_analysis(test_analysis_id, user_id)
            if cancelled:
                print(f"✅ Analyse annulée avec succès")
                
                # Vérifier le statut
                analysis = AnalysisManager.get_analysis_by_id(test_analysis_id)
                if analysis and analysis.get('status') == 'cancelled':
                    print(f"✅ Statut correctement mis à jour: {analysis['status']}")
                    print(f"✅ Message d'erreur: {analysis.get('error_message')}")
                else:
                    print(f"❌ Statut incorrect: {analysis.get('status') if analysis else 'analyse non trouvée'}")
            else:
                print(f"❌ Échec de l'annulation")
            
            # Tester l'annulation d'une analyse déjà annulée (doit échouer)
            cancelled_again = AnalysisManager.cancel_analysis(test_analysis_id, user_id)
            if not cancelled_again:
                print(f"✅ Impossible d'annuler une analyse déjà annulée (comportement correct)")
            else:
                print(f"❌ L'analyse a été annulée deux fois (comportement incorrect)")
            
            # Nettoyer
            mongo.db.analyses_scan.delete_one({'analysis_id': test_analysis_id})
            print(f"🧹 Analyse de test supprimée")
            
        except Exception as e:
            print(f"❌ Erreur lors du test: {e}")
            # Nettoyer en cas d'erreur
            mongo.db.analyses_scan.delete_one({'analysis_id': test_analysis_id})

def test_cancel_permissions():
    """Teste les permissions d'annulation"""
    print("\n🔒 Test des permissions d'annulation...")
    
    app = create_app()
    with app.app_context():
        # Trouver deux utilisateurs différents
        users = list(mongo.db.users.find({}, {'_id': 1, 'username': 1}).limit(2))
        if len(users) < 2:
            print("⚠️ Pas assez d'utilisateurs pour tester les permissions")
            return
        
        user1_id = str(users[0]['_id'])
        user2_id = str(users[1]['_id'])
        
        print(f"👤 Utilisateur 1: {users[0]['username']} ({user1_id})")
        print(f"👤 Utilisateur 2: {users[1]['username']} ({user2_id})")
        
        test_analysis_id = "test-permissions-456"
        
        try:
            # Créer une analyse pour l'utilisateur 1
            mongo_id = AnalysisManager.create_analysis(
                user_id=user1_id,
                analysis_id=test_analysis_id,
                file_info={
                    'filename': 'test_permissions.csv',
                    'size': 1024,
                    'content_type': 'text/csv'
                },
                analysis_type='basic'
            )
            print(f"✅ Analyse créée pour l'utilisateur 1")
            
            # Mettre en statut running
            AnalysisManager.update_analysis_status(test_analysis_id, 'running')
            
            # Tenter d'annuler avec l'utilisateur 2 (doit échouer)
            cancelled_by_other = AnalysisManager.cancel_analysis(test_analysis_id, user2_id)
            if not cancelled_by_other:
                print(f"✅ L'utilisateur 2 ne peut pas annuler l'analyse de l'utilisateur 1 (sécurité OK)")
            else:
                print(f"❌ L'utilisateur 2 a pu annuler l'analyse de l'utilisateur 1 (problème de sécurité)")
            
            # Annuler avec le bon utilisateur
            cancelled_by_owner = AnalysisManager.cancel_analysis(test_analysis_id, user1_id)
            if cancelled_by_owner:
                print(f"✅ L'utilisateur 1 peut annuler sa propre analyse")
            else:
                print(f"❌ L'utilisateur 1 ne peut pas annuler sa propre analyse")
            
            # Nettoyer
            mongo.db.analyses_scan.delete_one({'analysis_id': test_analysis_id})
            print(f"🧹 Analyse de test supprimée")
            
        except Exception as e:
            print(f"❌ Erreur lors du test de permissions: {e}")
            mongo.db.analyses_scan.delete_one({'analysis_id': test_analysis_id})

def show_current_analyses():
    """Affiche les analyses actuelles"""
    print("\n📊 Analyses actuelles dans la base:")
    
    app = create_app()
    with app.app_context():
        analyses = list(mongo.db.analyses_scan.find({}, {
            'analysis_id': 1, 
            'status': 1, 
            'user_id': 1, 
            'created_at': 1
        }).sort('created_at', -1))
        
        if not analyses:
            print("  Aucune analyse trouvée")
            return
        
        for analysis in analyses:
            user_id = analysis.get('user_id')
            if isinstance(user_id, ObjectId):
                user = mongo.db.users.find_one({'_id': user_id}, {'username': 1})
                username = user['username'] if user else 'Unknown'
            else:
                username = str(user_id)
            
            print(f"  - {analysis['analysis_id'][:20]}... | {analysis['status']} | {username}")

if __name__ == "__main__":
    print("🚀 Tests de la fonctionnalité d'annulation d'analyse")
    
    # Afficher les analyses actuelles
    show_current_analyses()
    
    # Tester l'annulation
    test_cancel_analysis()
    
    # Tester les permissions
    test_cancel_permissions()
    
    print("\n✅ Tests terminés !")
