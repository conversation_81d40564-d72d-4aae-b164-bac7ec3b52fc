from flask import Blueprint, jsonify, request
from app.extensions import mongo
from bson import ObjectId
from app.utils.decorators import admin_required, handle_options
from app.utils.hash_utils import hash_password_custom
from app.utils.helpers import (
    is_valid_name, is_valid_date, is_strong_password,
    is_valid_email, is_valid_username
)
from app.models.session import SessionManager
from app.utils.activity_logger import log_user_management, log_session_terminated
from datetime import datetime

admin_bp = Blueprint("admin_bp", __name__, url_prefix="/admin")

# 1. Lister tous les utilisateurs
@admin_bp.route("/users", methods=["GET", "OPTIONS"])
@handle_options
@admin_required
def get_all_users():
    users = mongo.db.users.find({}, {"password": 0})
    result = []
    for user in users:
        user["_id"] = str(user["_id"])
        result.append(user)
    return jsonify(result), 200

# 2. C<PERSON>er un nouvel utilisateur
@admin_bp.route("/users", methods=["POST"])
@admin_required
def create_user():
    data = request.get_json()
    required = ["first_name", "last_name", "date_of_birth", "username", "gender", "email", "password"]
    if not all(field in data for field in required):
        return jsonify({"msg": "Missing required fields"}), 400

    if not is_valid_name(data["first_name"]) or not is_valid_name(data["last_name"]):
        return jsonify({"msg": "Invalid name format"}), 400
    if not is_valid_date(data["date_of_birth"]):
        return jsonify({"msg": "Date must be DD/MM/YYYY"}), 400
    if not is_valid_username(data["username"]):
        return jsonify({"msg": "Invalid username format"}), 400
    if not is_valid_email(data["email"]):
        return jsonify({"msg": "Invalid email format"}), 400
    if not is_strong_password(data["password"]):
        return jsonify({"msg": "Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule, un chiffre et un symbole"}), 400

    if mongo.db.users.find_one({"email": data["email"]}) or mongo.db.users.find_one({"username": data["username"]}):
        return jsonify({"msg": "Email or username already exists"}), 400

    user = {
        "first_name": data["first_name"],
        "last_name": data["last_name"],
        "date_of_birth": data["date_of_birth"],
        "username": data["username"],
        "gender": data["gender"],
        "email": data["email"],
        "password": hash_password_custom(data["password"]),
        "role": data.get("role", "user"),
        "banned": False,
        "active": True,
        "email_verified": True,  # Admin-created users are pre-verified
        "2fa_enabled": False,
        "created_at": datetime.utcnow()
    }

    result = mongo.db.users.insert_one(user)

    # Log user creation activity
    log_user_management(
        action='create',
        target_user_id=str(result.inserted_id),
        details={
            "username": data["username"],
            "email": data["email"],
            "role": data.get("role", "user"),
            "created_by_admin": True
        }
    )

    return jsonify({"msg": "User created successfully"}), 201

# 3. Voir un utilisateur par ID
@admin_bp.route("/users/<user_id>", methods=["GET"])
@admin_required
def get_user(user_id):
    user = mongo.db.users.find_one({"_id": ObjectId(user_id)}, {"password": 0})
    if not user:
        return jsonify({"msg": "User not found"}), 404
    user["_id"] = str(user["_id"])
    return jsonify(user), 200

# 4. Modifier un utilisateur (profil + mot de passe + rôle)
@admin_bp.route("/users/<user_id>", methods=["PUT"])
@admin_required
def update_user(user_id):
    data = request.get_json()
    update_fields = {}

    for field in ["first_name", "last_name", "gender", "date_of_birth", "username", "email"]:
        if field in data:
            update_fields[field] = data[field]

    if "role" in data:
        if data["role"] in ["user", "admin"]:
            update_fields["role"] = data["role"]
        else:
            return jsonify({"msg": "Invalid role value"}), 400

    if "password" in data:
        if not is_strong_password(data["password"]):
            return jsonify({"msg": "Le mot de passe doit contenir au moins 8 caractères, une majuscule, une minuscule, un chiffre et un symbole"}), 400
        update_fields["password"] = hash_password_custom(data["password"])

    if not update_fields:
        return jsonify({"msg": "No valid fields to update"}), 400

    result = mongo.db.users.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": update_fields}
    )

    if result.matched_count == 0:
        return jsonify({"msg": "User not found"}), 404

    return jsonify({"msg": "User updated successfully"}), 200

# 5. Supprimer un utilisateur
@admin_bp.route("/users/<user_id>", methods=["DELETE"])
@admin_required
def delete_user(user_id):
    # Get user info before deletion
    user = mongo.db.users.find_one({"_id": ObjectId(user_id)})
    if not user:
        return jsonify({"msg": "User not found"}), 404

    # Terminate all active sessions for the user before deletion
    SessionManager.end_all_user_sessions(user_id)

    # Delete the user
    result = mongo.db.users.delete_one({"_id": ObjectId(user_id)})
    if result.deleted_count == 0:
        return jsonify({"msg": "User not found"}), 404

    # Log user deletion activity
    log_user_management(
        action='delete',
        target_user_id=user_id,
        details={
            "username": user.get("username"),
            "email": user.get("email"),
            "reason": "admin_action",
            "sessions_terminated": True
        }
    )

    return jsonify({"msg": "User deleted and all sessions terminated"}), 200

# 6. Bannir un utilisateur
@admin_bp.route("/users/<user_id>/ban", methods=["PUT"])
@admin_required
def ban_user(user_id):
    # Get user info before banning
    user = mongo.db.users.find_one({"_id": ObjectId(user_id)})
    if not user:
        return jsonify({"msg": "User not found"}), 404

    # Ban the user
    result = mongo.db.users.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": {"banned": True}}
    )

    # Terminate all active sessions for the banned user
    SessionManager.end_all_user_sessions(user_id)

    # Log user ban activity
    log_user_management(
        action='ban',
        target_user_id=user_id,
        details={
            "username": user.get("username"),
            "email": user.get("email"),
            "reason": "admin_action",
            "sessions_terminated": True
        }
    )

    return jsonify({"msg": "User has been banned and all sessions terminated"}), 200

# 7. Débannir un utilisateur
@admin_bp.route("/users/<user_id>/unban", methods=["PUT"])
@admin_required
def unban_user(user_id):
    # Get user info before unbanning
    user = mongo.db.users.find_one({"_id": ObjectId(user_id)})
    if not user:
        return jsonify({"msg": "User not found"}), 404

    result = mongo.db.users.update_one(
        {"_id": ObjectId(user_id)},
        {"$set": {"banned": False}}
    )

    # Log user unban activity
    log_user_management(
        action='unban',
        target_user_id=user_id,
        details={
            "username": user.get("username"),
            "email": user.get("email"),
            "reason": "admin_action"
        }
    )

    return jsonify({"msg": "User has been unbanned"}), 200

# 8. Get user sessions and login history
@admin_bp.route("/users/<user_id>/sessions", methods=["GET", "OPTIONS"])
@handle_options
@admin_required
def get_user_sessions(user_id):
    try:
        # Check if user exists
        user = mongo.db.users.find_one({"_id": ObjectId(user_id)})
        if not user:
            return jsonify({"msg": "User not found"}), 404

        # Get query parameters
        limit = int(request.args.get('limit', 50))
        active_only = request.args.get('active_only', 'false').lower() == 'true'

        # Get sessions
        sessions = SessionManager.get_user_sessions(user_id, limit=limit, active_only=active_only)

        # Convert ObjectId to string for JSON serialization
        for session in sessions:
            session["_id"] = str(session["_id"])
            session["user_id"] = str(session["user_id"])

        return jsonify(sessions), 200

    except Exception as e:
        return jsonify({"msg": "Failed to fetch user sessions", "error": str(e)}), 500

# 9. Get user session statistics
@admin_bp.route("/users/<user_id>/sessions/stats", methods=["GET", "OPTIONS"])
@handle_options
@admin_required
def get_user_session_stats(user_id):
    try:
        # Check if user exists
        user = mongo.db.users.find_one({"_id": ObjectId(user_id)})
        if not user:
            return jsonify({"msg": "User not found"}), 404

        # Get session statistics
        stats = SessionManager.get_session_stats(user_id)

        # Convert datetime objects to ISO format for JSON serialization
        if stats.get("first_login"):
            stats["first_login"] = stats["first_login"].isoformat()
        if stats.get("last_login"):
            stats["last_login"] = stats["last_login"].isoformat()

        return jsonify(stats), 200

    except Exception as e:
        return jsonify({"msg": "Failed to fetch session statistics", "error": str(e)}), 500

# 10. End all user sessions (security action)
@admin_bp.route("/users/<user_id>/sessions/end-all", methods=["POST", "OPTIONS"])
@handle_options
@admin_required
def end_all_user_sessions(user_id):
    try:
        # Check if user exists
        user = mongo.db.users.find_one({"_id": ObjectId(user_id)})
        if not user:
            return jsonify({"msg": "User not found"}), 404

        # End all user sessions
        SessionManager.end_all_user_sessions(user_id)

        return jsonify({"msg": "All user sessions have been terminated"}), 200

    except Exception as e:
        return jsonify({"msg": "Failed to end user sessions", "error": str(e)}), 500

# 11. End specific session
@admin_bp.route("/sessions/<session_id>/end", methods=["POST", "OPTIONS"])
@handle_options
@admin_required
def end_specific_session(session_id):
    try:
        # Get session info before terminating
        session = mongo.db.user_sessions.find_one({"session_id": session_id})
        if not session:
            return jsonify({"msg": "Session not found"}), 404

        # End the specific session
        SessionManager.end_session(session_id)

        # Log session termination activity
        from flask_jwt_extended import get_jwt_identity
        admin_user_id = get_jwt_identity()

        log_session_terminated(
            user_id=str(session.get("user_id")),
            terminated_by=admin_user_id,
            details={
                "session_id": session_id,
                "terminated_by_admin": True,
                "ip_address": session.get("ip_address"),
                "device_info": session.get("device_info", {})
            }
        )

        return jsonify({"msg": "Session terminated successfully"}), 200

    except Exception as e:
        return jsonify({"msg": "Failed to end session", "error": str(e)}), 500
