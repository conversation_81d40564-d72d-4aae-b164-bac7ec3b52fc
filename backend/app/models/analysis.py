"""
Modèle pour les analyses IA dans PICA.
Stocke les informations des analyses lancées vers le serveur externe.
"""

from datetime import datetime, timezone
from typing import Optional, Dict, Any, List
from bson import ObjectId
from app.extensions import mongo


class AnalysisManager:
    """Gestionnaire pour les analyses IA"""
    
    @staticmethod
    def create_analysis(
        user_id: str,
        analysis_id: str,
        file_info: Dict[str, Any],
        analysis_type: str = 'full',
        scan_ids: Optional[List[str]] = None
    ) -> str:
        """
        Crée une nouvelle analyse dans la base de données.
        
        Args:
            user_id: ID de l'utilisateur qui lance l'analyse
            analysis_id: ID de l'analyse retourné par le serveur IA
            file_info: Informations sur le fichier analysé
            analysis_type: Type d'analyse ('full' ou 'basic')
            scan_ids: Liste des scan IDs (pour analyses basiques)
            
        Returns:
            str: ID MongoDB de l'analyse créée
        """
        # Convert user_id to ObjectId if it's a string (for consistency)
        from bson import ObjectId
        if isinstance(user_id, str) and len(user_id) == 24:
            try:
                user_object_id = ObjectId(user_id)
            except:
                # If it's not a valid ObjectId, it might be a username - convert it
                user_doc = mongo.db.users.find_one({'username': user_id})
                if user_doc:
                    user_object_id = user_doc['_id']
                else:
                    raise ValueError(f"User not found: {user_id}")
        elif isinstance(user_id, ObjectId):
            user_object_id = user_id
        else:
            # Assume it's a username, convert to ObjectId
            user_doc = mongo.db.users.find_one({'username': user_id})
            if user_doc:
                user_object_id = user_doc['_id']
            else:
                raise ValueError(f"User not found: {user_id}")

        analysis_data = {
            'user_id': user_object_id,  # Store ObjectId for consistency
            'analysis_id': analysis_id,  # ID du serveur IA externe
            'status': 'pending',
            'analysis_type': analysis_type,
            'file_info': file_info,
            'scan_ids': scan_ids or [],
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc),
            'started_at': datetime.now(timezone.utc),
            'completed_at': None,
            'progress': 0,
            'error_message': None,
            'report_data': None,
            'server_response': None
        }
        
        result = mongo.db.analyses_scan.insert_one(analysis_data)
        return str(result.inserted_id)
    
    @staticmethod
    def update_analysis_status(
        analysis_id: str,
        status: str,
        progress: Optional[int] = None,
        error_message: Optional[str] = None,
        server_response: Optional[Dict] = None
    ) -> bool:
        """
        Met à jour le statut d'une analyse.
        
        Args:
            analysis_id: ID de l'analyse (serveur IA)
            status: Nouveau statut
            progress: Progression (0-100)
            error_message: Message d'erreur si applicable
            server_response: Réponse complète du serveur
            
        Returns:
            bool: True si la mise à jour a réussi
        """
        update_data = {
            'status': status,
            'updated_at': datetime.now(timezone.utc)
        }
        
        if progress is not None:
            update_data['progress'] = progress
            
        if error_message:
            update_data['error_message'] = error_message
            
        if server_response:
            update_data['server_response'] = server_response
            
        if status == 'completed':
            update_data['completed_at'] = datetime.now(timezone.utc)
            update_data['progress'] = 100
            
        result = mongo.db.analyses_scan.update_one(
            {'analysis_id': analysis_id},
            {'$set': update_data}
        )
        
        return result.modified_count > 0
    
    @staticmethod
    def save_analysis_report(analysis_id: str, report_data: Dict[str, Any]) -> bool:
        """
        Sauvegarde le rapport d'analyse.
        
        Args:
            analysis_id: ID de l'analyse (serveur IA)
            report_data: Données du rapport
            
        Returns:
            bool: True si la sauvegarde a réussi
        """
        update_data = {
            'report_data': report_data,
            'status': 'completed',
            'completed_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc),
            'progress': 100
        }
        
        result = mongo.db.analyses_scan.update_one(
            {'analysis_id': analysis_id},
            {'$set': update_data}
        )
        
        return result.modified_count > 0
    
    @staticmethod
    def cancel_analysis(analysis_id: str, user_id: str) -> bool:
        """
        Annule une analyse en cours.

        Args:
            analysis_id: ID de l'analyse (serveur IA)
            user_id: ID de l'utilisateur qui demande l'annulation

        Returns:
            bool: True si l'annulation a réussi
        """
        # Convertir user_id si nécessaire
        from bson import ObjectId
        if isinstance(user_id, str) and len(user_id) == 24:
            try:
                user_object_id = ObjectId(user_id)
            except:
                user_doc = mongo.db.users.find_one({'username': user_id})
                if user_doc:
                    user_object_id = user_doc['_id']
                else:
                    user_object_id = user_id
        else:
            user_doc = mongo.db.users.find_one({'username': user_id})
            if user_doc:
                user_object_id = user_doc['_id']
            else:
                user_object_id = user_id

        # Vérifier que l'analyse existe et appartient à l'utilisateur
        analysis = mongo.db.analyses_scan.find_one({
            'analysis_id': analysis_id,
            '$or': [
                {'user_id': user_object_id},
                {'user_id': user_id}  # Legacy compatibility
            ]
        })

        if not analysis:
            return False

        # Vérifier que l'analyse peut être annulée (statut pending ou running)
        if analysis.get('status') not in ['pending', 'running']:
            return False

        # Mettre à jour le statut en base
        result = mongo.db.analyses_scan.update_one(
            {'analysis_id': analysis_id},
            {
                '$set': {
                    'status': 'cancelled',
                    'updated_at': datetime.now(timezone.utc),
                    'completed_at': datetime.now(timezone.utc),
                    'error_message': 'Analysis cancelled by user'
                }
            }
        )

        return result.modified_count > 0

    @staticmethod
    def get_analysis_by_id(analysis_id: str) -> Optional[Dict[str, Any]]:
        """
        Récupère une analyse par son ID.
        
        Args:
            analysis_id: ID de l'analyse (serveur IA)
            
        Returns:
            Dict ou None: Données de l'analyse
        """
        return mongo.db.analyses_scan.find_one({'analysis_id': analysis_id})
    
    @staticmethod
    def get_user_analyses(
        user_id: str,
        page: int = 1,
        limit: int = 20,
        status_filter: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Récupère les analyses d'un utilisateur avec pagination.

        Args:
            user_id: ID de l'utilisateur (ObjectId string ou username)
            page: Numéro de page
            limit: Nombre d'éléments par page
            status_filter: Filtre par statut (optionnel)

        Returns:
            Dict: Analyses avec métadonnées de pagination
        """
        # Convert user_id to ObjectId if needed
        from bson import ObjectId
        if isinstance(user_id, str) and len(user_id) == 24:
            try:
                user_object_id = ObjectId(user_id)
            except:
                # If it's not a valid ObjectId, it might be a username - convert it
                user_doc = mongo.db.users.find_one({'username': user_id})
                if user_doc:
                    user_object_id = user_doc['_id']
                else:
                    user_object_id = user_id  # Fallback for legacy data
        else:
            # Assume it's a username, convert to ObjectId
            user_doc = mongo.db.users.find_one({'username': user_id})
            if user_doc:
                user_object_id = user_doc['_id']
            else:
                user_object_id = user_id  # Fallback for legacy data

        # Query with both ObjectId and legacy username for compatibility
        query = {'$or': [
            {'user_id': user_object_id},
            {'user_id': user_id}  # Legacy compatibility
        ]}
        
        if status_filter:
            query['status'] = status_filter
            
        # Compter le total
        total = mongo.db.analyses_scan.count_documents(query)

        # Récupérer les analyses avec pagination
        skip = (page - 1) * limit
        analyses = list(
            mongo.db.analyses_scan.find(query)
            .sort('created_at', -1)  # Plus récentes en premier
            .skip(skip)
            .limit(limit)
        )
        
        # Convertir ObjectId en string pour JSON
        for analysis in analyses:
            analysis['_id'] = str(analysis['_id'])
            # user_id is already a string (username), no conversion needed

            # Ajouter generate_full_report basé sur analysis_type pour compatibilité frontend
            analysis['generate_full_report'] = analysis.get('analysis_type') == 'full'
            
        return {
            'analyses': analyses,
            'total': total,
            'page': page,
            'limit': limit,
            'pages': (total + limit - 1) // limit
        }
    
    @staticmethod
    def get_all_analyses(
        page: int = 1,
        limit: int = 20,
        status_filter: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Récupère toutes les analyses (pour les admins).
        
        Args:
            page: Numéro de page
            limit: Nombre d'éléments par page
            status_filter: Filtre par statut (optionnel)
            
        Returns:
            Dict: Analyses avec métadonnées de pagination
        """
        query = {}
        
        if status_filter:
            query['status'] = status_filter
            
        # Compter le total
        total = mongo.db.analyses_scan.count_documents(query)

        # Récupérer les analyses avec pagination
        skip = (page - 1) * limit
        analyses = list(
            mongo.db.analyses_scan.find(query)
            .sort('created_at', -1)
            .skip(skip)
            .limit(limit)
        )
        
        # Convertir ObjectId en string et ajouter info utilisateur
        for analysis in analyses:
            analysis['_id'] = str(analysis['_id'])
            username = analysis['user_id']  # user_id is already a username

            # Récupérer les infos utilisateur par username
            user = mongo.db.users.find_one({'username': username}, {'username': 1, 'email': 1})
            if user:
                analysis['user_info'] = {
                    'username': user.get('username'),
                    'email': user.get('email')
                }

            # Ajouter generate_full_report basé sur analysis_type pour compatibilité frontend
            analysis['generate_full_report'] = analysis.get('analysis_type') == 'full'
            
        return {
            'analyses': analyses,
            'total': total,
            'page': page,
            'limit': limit,
            'pages': (total + limit - 1) // limit
        }
    
    @staticmethod
    def delete_analysis(analysis_id: str, user_id: Optional[str] = None) -> bool:
        """
        Supprime une analyse.
        
        Args:
            analysis_id: ID de l'analyse (serveur IA)
            user_id: ID de l'utilisateur (pour vérifier les permissions)
            
        Returns:
            bool: True si la suppression a réussi
        """
        query = {'analysis_id': analysis_id}
        
        if user_id:
            query['user_id'] = user_id
            
        result = mongo.db.analyses_scan.delete_one(query)
        return result.deleted_count > 0
    
    @staticmethod
    def get_analysis_statistics(user_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Récupère les statistiques des analyses.

        Args:
            user_id: ID de l'utilisateur (optionnel, pour stats personnelles)

        Returns:
            Dict: Statistiques des analyses
        """
        query = {}
        if user_id:
            # Convert user_id to ObjectId if needed
            from bson import ObjectId
            if isinstance(user_id, str) and len(user_id) == 24:
                try:
                    user_object_id = ObjectId(user_id)
                except:
                    # If it's not a valid ObjectId, it might be a username - convert it
                    user_doc = mongo.db.users.find_one({'username': user_id})
                    if user_doc:
                        user_object_id = user_doc['_id']
                    else:
                        user_object_id = user_id  # Fallback for legacy data
            else:
                # Assume it's a username, convert to ObjectId
                user_doc = mongo.db.users.find_one({'username': user_id})
                if user_doc:
                    user_object_id = user_doc['_id']
                else:
                    user_object_id = user_id  # Fallback for legacy data

            # Query with both ObjectId and legacy username for compatibility
            query = {'$or': [
                {'user_id': user_object_id},
                {'user_id': user_id}  # Legacy compatibility
            ]}
            
        pipeline = [
            {'$match': query},
            {
                '$group': {
                    '_id': '$status',
                    'count': {'$sum': 1}
                }
            }
        ]
        
        status_counts = list(mongo.db.analyses_scan.aggregate(pipeline))

        # Compter par type d'analyse
        type_pipeline = [
            {'$match': query},
            {
                '$group': {
                    '_id': '$analysis_type',
                    'count': {'$sum': 1}
                }
            }
        ]

        type_counts = list(mongo.db.analyses_scan.aggregate(type_pipeline))

        # Construire les statistiques
        stats = {
            'total_analyses': mongo.db.analyses_scan.count_documents(query),
            'completed_analyses': 0,
            'failed_analyses': 0,
            'pending_analyses': 0,
            'running_analyses': 0,
            'full_reports': 0,
            'basic_reports': 0
        }
        
        for status_count in status_counts:
            status = status_count['_id']
            count = status_count['count']
            
            if status == 'completed':
                stats['completed_analyses'] = count
            elif status in ['failed', 'error']:
                stats['failed_analyses'] = count
            elif status == 'pending':
                stats['pending_analyses'] = count
            elif status == 'running':
                stats['running_analyses'] = count
                
        for type_count in type_counts:
            analysis_type = type_count['_id']
            count = type_count['count']
            
            if analysis_type == 'full':
                stats['full_reports'] = count
            elif analysis_type == 'basic':
                stats['basic_reports'] = count
                
        return stats
