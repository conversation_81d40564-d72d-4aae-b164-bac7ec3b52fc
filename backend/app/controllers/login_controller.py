from flask import request, jsonify
from ..extensions import mongo
from ..utils.hash_utils import verify_password_custom
from ..utils.email_utils import send_async_email
from ..services.email_template_service import EmailTemplateService
from ..models.session import SessionManager
from ..utils.activity_logger import log_login_attempt, log_logout
from flask_jwt_extended import (
    create_access_token,
    create_refresh_token,
    jwt_required,
    get_jwt_identity
)
from datetime import timedelta
import random

login_otp_store = {}

def login():
    data = request.get_json()
    email = data.get("email")
    password = data.get("password")
    remember_me = data.get("remember_me", False)

    # Debug logs
    print(f"🔍 Backend login - Data received: {data}")
    print(f"🔍 Backend login - remember_me value: {remember_me} (type: {type(remember_me)})")

    user = mongo.db.users.find_one({"email": email})
    if not user:
        # Log failed login attempt for non-existent user
        log_login_attempt(
            username=email,
            success=False,
            ip_address=request.remote_addr,
            details={
                "email": email,
                "reason": "user_not_found"
            }
        )
        return jsonify({"msg": "User not found"}), 404
    if user.get("banned"):
        return jsonify({"msg": "Your account has been banned"}), 403
    if not user.get("email_verified", False):
        return jsonify({"msg": "Please verify your email before logging in"}), 403
    if not verify_password_custom(password, user["password"]):
        # Log failed login attempt
        log_login_attempt(
            username=user.get("username", email),
            success=False,
            ip_address=request.remote_addr,
            details={
                "email": email,
                "reason": "invalid_password"
            }
        )
        return jsonify({"msg": "Invalid credentials"}), 401

    if user.get("2fa_enabled", False):
        otp = str(random.randint(100000, 999999))
        login_otp_store[email] = {"otp": otp, "user": user, "remember_me": remember_me}

        # Send beautiful OTP email using template
        EmailTemplateService.send_otp_email(
            email=email,
            otp=otp
        )

        return jsonify({"msg": "OTP sent to email", "2fa": True}), 200

    # Set token expiration based on remember_me option
    access_token_expiration = timedelta(hours=24) if remember_me else timedelta(hours=2)
    print(f"🔍 Backend login - Token expiration: {'24h (Remember Me)' if remember_me else '2h (Normal)'}")

    # Get client information for session tracking
    ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
    user_agent = request.headers.get('User-Agent', '')

    # Create session record
    session = SessionManager.create_session(
        user_id=str(user["_id"]),
        ip_address=ip_address,
        user_agent=user_agent,
        remember_me=remember_me
    )

    # Create access token with session ID
    access_token = create_access_token(
        identity=user["username"],
        additional_claims={
            "id": str(user["_id"]),
            "username": user["username"],
            "email": user["email"],
            "role": user.get("role", "user"),
            "session_id": session["session_id"]
        },
        expires_delta=access_token_expiration
    )

    # Create refresh token only if remember_me is False (for active users with 2h tokens)
    refresh_token = None
    if not remember_me:
        print("🔍 Backend login - Creating refresh token (no Remember Me)")
        refresh_token = create_refresh_token(
            identity=user["username"],
            additional_claims={
                "id": str(user["_id"]),
                "username": user["username"],
                "email": user["email"],
                "role": user.get("role", "user"),
                "session_id": session["session_id"]
            },
            expires_delta=timedelta(hours=24)  # Refresh token valide 24h pour les sessions courtes
        )
    else:
        print("🔍 Backend login - No refresh token (Remember Me enabled)")

    # Log successful login
    log_login_attempt(
        username=user["username"],
        success=True,
        ip_address=request.remote_addr,
        details={
            "email": user["email"],
            "role": user.get("role", "user"),
            "remember_me": remember_me,
            "session_id": session["session_id"]
        }
    )

    return jsonify({
        "msg": "Login successful",
        "token": access_token,
        "refresh_token": refresh_token,
        "id": str(user["_id"]),
        "username": user["username"],
        "email": user["email"],
        "2fa": False,
        "role": user.get("role", "user")
    }), 200

def verify_otp():
    data = request.get_json()
    email = data.get("email")
    otp = data.get("otp")

    record = login_otp_store.get(email)
    if not record:
        return jsonify({"msg": "No login in progress"}), 400
    if record["otp"] != otp:
        return jsonify({"msg": "Invalid OTP"}), 400

    user = record["user"]
    remember_me = record.get("remember_me", False)
    del login_otp_store[email]

    # Set token expiration based on remember_me option
    access_token_expiration = timedelta(hours=24) if remember_me else timedelta(hours=2)

    # Get client information for session tracking
    ip_address = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'unknown'))
    user_agent = request.headers.get('User-Agent', '')

    # Create session record
    session = SessionManager.create_session(
        user_id=str(user["_id"]),
        ip_address=ip_address,
        user_agent=user_agent,
        remember_me=remember_me
    )

    # Create access token with session ID
    access_token = create_access_token(
        identity=user["username"],
        additional_claims={
            "id": str(user["_id"]),
            "username": user["username"],
            "email": user["email"],
            "role": user.get("role", "user"),
            "session_id": session["session_id"]
        },
        expires_delta=access_token_expiration
    )

    # Create refresh token only if remember_me is False (for active users with 2h tokens)
    refresh_token = None
    if not remember_me:
        refresh_token = create_refresh_token(
            identity=user["username"],
            additional_claims={
                "id": str(user["_id"]),
                "username": user["username"],
                "email": user["email"],
                "role": user.get("role", "user"),
                "session_id": session["session_id"]
            },
            expires_delta=timedelta(hours=24)  # Refresh token valide 24h pour les sessions courtes
        )

    # Log successful login (OTP case)
    log_login_attempt(
        username=user["username"],
        success=True,
        ip_address=request.remote_addr,
        details={
            "email": user["email"],
            "role": user.get("role", "user"),
            "remember_me": remember_me,
            "session_id": session["session_id"],
            "otp_verified": True
        }
    )

    return jsonify({
        "msg": "Login successful",
        "token": access_token,
        "refresh_token": refresh_token,
        "id": str(user["_id"]),
        "username": user["username"],
        "email": user["email"],
        "role": user.get("role", "user")
    }), 200

@jwt_required(refresh=True)
def refresh_token():
    """
    Endpoint pour rafraîchir le token d'accès en utilisant un refresh token
    """
    try:
        # Obtenir l'identité de l'utilisateur depuis le refresh token
        current_user_identity = get_jwt_identity()

        # Récupérer les informations utilisateur depuis la base de données
        user = mongo.db.users.find_one({"username": current_user_identity})
        if not user:
            return jsonify({"msg": "User not found"}), 404

        if user.get("banned"):
            return jsonify({"msg": "Account has been banned"}), 403

        # Créer un nouveau token d'accès avec les mêmes claims
        new_access_token = create_access_token(
            identity=current_user_identity,
            additional_claims={
                "id": str(user["_id"]),
                "username": user["username"],
                "email": user["email"],
                "role": user.get("role", "user")
            },
            expires_delta=timedelta(hours=2)  # Token d'accès standard de 2h
        )

        return jsonify({
            "msg": "Token refreshed successfully",
            "token": new_access_token,
            "user": {
                "id": str(user["_id"]),
                "username": user["username"],
                "email": user["email"],
                "role": user.get("role", "user")
            }
        }), 200

    except Exception as e:
        return jsonify({"msg": "Failed to refresh token", "error": str(e)}), 400

@jwt_required()
def logout():
    """Handle user logout and end session"""
    try:
        from flask_jwt_extended import get_jwt

        # Get session ID from token claims
        claims = get_jwt()
        session_id = claims.get("session_id")
        user_id = get_jwt_identity()

        if session_id:
            # End the session
            SessionManager.end_session(session_id)

        # Log logout activity
        log_logout(
            user_id=user_id,
            details={
                "session_id": session_id,
                "logout_type": "user_initiated"
            }
        )

        return jsonify({"msg": "Logged out successfully"}), 200

    except Exception as e:
        return jsonify({"msg": "Logout completed", "note": "Session cleanup may have failed"}), 200

def toggle_2fa():
    user_email = get_jwt_identity()
    data = request.get_json()
    enable_2fa = data.get("enabled", False)

    result = mongo.db.users.update_one(
        {"email": user_email},
        {"$set": {"2fa_enabled": enable_2fa}}
    )

    if result.modified_count:
        return jsonify({"msg": f"2FA {'enabled' if enable_2fa else 'disabled'}"}), 200
    return jsonify({"msg": "No change made"}), 400
