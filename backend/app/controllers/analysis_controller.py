"""
Contrôleur pour la gestion des analyses IA dans PICA.
Gère le stockage et la récupération des analyses depuis MongoDB.
"""

import requests
from flask import Blueprint, request, jsonify, current_app
from werkzeug.utils import secure_filename
from typing import Dict, Any
from flask_jwt_extended import jwt_required, get_jwt_identity

from ..models.analysis import AnalysisManager
from ..utils.decorators import handle_options

# Créer le blueprint pour les analyses
analysis_bp = Blueprint('analysis', __name__)

# Configuration du serveur IA externe
EXTERNAL_AI_SERVER = "http://82.165.144.72:4001"

def success_response(data=None, message="Success"):
    """Helper pour les réponses de succès"""
    response = {"status": "success", "message": message}
    if data:
        response.update(data)
    return response

def error_response(message="Error", error=None, status_code=400):
    """Helper pour les réponses d'erreur"""
    response = {"status": "error", "message": message}
    if error:
        response["error"] = error
    return jsonify(response), status_code

@analysis_bp.route('/analysis/store', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def store_analysis():
    """
    Stocke les informations d'une analyse lancée directement sur le serveur IA.

    Expected JSON:
        analysis_id: ID de l'analyse retourné par le serveur IA
        file_info: Informations sur le fichier (filename, size, content_type)
        analysis_type: Type d'analyse ('full' ou 'basic')
        scan_ids: Liste des scan IDs (pour analyses basiques)

    Returns:
        JSON: Confirmation du stockage
    """
    try:
        user_id = get_jwt_identity()
        if not user_id:
            return error_response("Utilisateur non authentifié", status_code=401)

        data = request.get_json()
        if not data:
            return error_response("Données JSON requises", "missing_json")

        # Vérifier les champs requis
        analysis_id = data.get('analysis_id')
        if not analysis_id:
            return error_response("ID d'analyse requis", "missing_analysis_id")

        file_info = data.get('file_info', {})
        analysis_type = data.get('analysis_type', 'full')
        scan_ids = data.get('scan_ids', [])

        # Stocker l'analyse dans MongoDB
        try:
            mongo_id = AnalysisManager.create_analysis(
                user_id=user_id,
                analysis_id=analysis_id,
                file_info=file_info,
                analysis_type=analysis_type,
                scan_ids=scan_ids
            )

            current_app.logger.info(f"Analyse stockée: {analysis_id} (MongoDB: {mongo_id})")

            return jsonify(success_response({
                'analysis_id': analysis_id,
                'mongo_id': mongo_id,
                'status': 'pending',
                'message': 'Analyse stockée avec succès en base'
            }))

        except Exception as e:
            current_app.logger.error(f"Erreur lors de la sauvegarde en base: {e}")
            return error_response("Erreur lors de la sauvegarde", str(e), 500)

    except Exception as e:
        current_app.logger.error(f"Erreur lors du stockage de l'analyse: {e}")
        return error_response("Erreur interne du serveur", str(e), 500)

@analysis_bp.route('/analysis/status/<analysis_id>', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_analysis_status(analysis_id):
    """
    Récupère le statut d'une analyse depuis le serveur IA et met à jour la base de données.

    Args:
        analysis_id: ID de l'analyse

    Returns:
        JSON: Statut et progression de l'analyse
    """
    try:
        user_id = get_jwt_identity()

        # Récupérer l'analyse depuis MongoDB
        analysis = AnalysisManager.get_analysis_by_id(analysis_id)
        if not analysis:
            return error_response("Analyse non trouvée", "analysis_not_found", 404)

        # Vérifier les permissions (sauf pour les admins)
        if analysis['user_id'] != user_id:
            # TODO: Vérifier si l'utilisateur est admin
            pass

        # Si l'analyse est déjà terminée, retourner les données stockées
        if analysis['status'] in ['completed', 'failed', 'error']:
            return jsonify(success_response({
                'analysis_id': analysis_id,
                'status': analysis['status'],
                'progress': analysis.get('progress', 0),
                'started_at': analysis['started_at'].isoformat() if analysis.get('started_at') else None,
                'completed_at': analysis['completed_at'].isoformat() if analysis.get('completed_at') else None,
                'error_message': analysis.get('error_message'),
                'file_info': analysis.get('file_info')
            }))

        # Sinon, interroger le serveur IA pour le statut actuel
        try:
            response = requests.get(
                f"{EXTERNAL_AI_SERVER}/status/{analysis_id}",
                timeout=10
            )
            response.raise_for_status()
            ai_status = response.json()

            # Mettre à jour le statut dans MongoDB
            AnalysisManager.update_analysis_status(
                analysis_id=analysis_id,
                status=ai_status.get('status', 'unknown'),
                progress=ai_status.get('progress', 0),
                server_response=ai_status
            )

            return jsonify(success_response(ai_status))

        except requests.exceptions.RequestException as e:
            current_app.logger.error(f"Erreur lors de la vérification du statut: {e}")

            # Retourner le statut stocké en cas d'erreur de communication
            return jsonify(success_response({
                'analysis_id': analysis_id,
                'status': analysis['status'],
                'progress': analysis.get('progress', 0),
                'started_at': analysis['started_at'].isoformat() if analysis.get('started_at') else None,
                'message': 'Statut depuis la base de données (serveur IA inaccessible)'
            }))

    except Exception as e:
        current_app.logger.error(f"Erreur lors de la récupération du statut: {e}")
        return error_response("Erreur interne du serveur", str(e), 500)

@analysis_bp.route('/analysis/update-status', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def update_analysis_status():
    """
    Met à jour le statut d'une analyse depuis le frontend après interrogation du serveur IA.

    Expected JSON:
        analysis_id: ID de l'analyse
        status: Nouveau statut
        progress: Progression (optionnel)
        error_message: Message d'erreur (optionnel)

    Returns:
        JSON: Confirmation de la mise à jour
    """
    try:
        user_id = get_jwt_identity()
        if not user_id:
            return error_response("Utilisateur non authentifié", status_code=401)

        data = request.get_json()
        if not data:
            return error_response("Données JSON requises", "missing_json")

        analysis_id = data.get('analysis_id')
        if not analysis_id:
            return error_response("ID d'analyse requis", "missing_analysis_id")

        # Vérifier que l'analyse appartient à l'utilisateur
        analysis = AnalysisManager.get_analysis_by_id(analysis_id)
        if not analysis:
            return error_response("Analyse non trouvée", "analysis_not_found", 404)

        if analysis['user_id'] != user_id:
            return error_response("Non autorisé", "unauthorized", 403)

        # Mettre à jour le statut
        success = AnalysisManager.update_analysis_status(
            analysis_id=analysis_id,
            status=data.get('status'),
            progress=data.get('progress'),
            error_message=data.get('error_message'),
            server_response=data.get('server_response')
        )

        if success:
            return jsonify(success_response({'message': 'Statut mis à jour avec succès'}))
        else:
            return error_response("Échec de la mise à jour", "update_failed", 500)

    except Exception as e:
        current_app.logger.error(f"Erreur lors de la mise à jour du statut: {e}")
        return error_response("Erreur interne du serveur", str(e), 500)

@analysis_bp.route('/analysis/save-report', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def save_analysis_report():
    """
    Sauvegarde le rapport d'une analyse récupéré depuis le serveur IA.

    Expected JSON:
        analysis_id: ID de l'analyse
        report_data: Données du rapport

    Returns:
        JSON: Confirmation de la sauvegarde
    """
    try:
        user_id = get_jwt_identity()
        if not user_id:
            return error_response("Utilisateur non authentifié", status_code=401)

        data = request.get_json()
        if not data:
            return error_response("Données JSON requises", "missing_json")

        analysis_id = data.get('analysis_id')
        if not analysis_id:
            return error_response("ID d'analyse requis", "missing_analysis_id")

        report_data = data.get('report_data')
        if not report_data:
            return error_response("Données du rapport requises", "missing_report_data")

        # Vérifier que l'analyse appartient à l'utilisateur
        analysis = AnalysisManager.get_analysis_by_id(analysis_id)
        if not analysis:
            return error_response("Analyse non trouvée", "analysis_not_found", 404)

        if analysis['user_id'] != user_id:
            return error_response("Non autorisé", "unauthorized", 403)

        # Sauvegarder le rapport
        success = AnalysisManager.save_analysis_report(analysis_id, report_data)

        if success:
            return jsonify(success_response({'message': 'Rapport sauvegardé avec succès'}))
        else:
            return error_response("Échec de la sauvegarde", "save_failed", 500)

    except Exception as e:
        current_app.logger.error(f"Erreur lors de la sauvegarde du rapport: {e}")
        return error_response("Erreur interne du serveur", str(e), 500)

@analysis_bp.route('/analysis/report/<analysis_id>', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_analysis_report(analysis_id):
    """
    Récupère le rapport d'une analyse et le stocke en base si nécessaire.
    
    Args:
        analysis_id: ID de l'analyse
        
    Returns:
        JSON: Rapport complet de l'analyse
    """
    try:
        user_id = get_jwt_identity()
        
        # Récupérer l'analyse depuis MongoDB
        analysis = AnalysisManager.get_analysis_by_id(analysis_id)
        if not analysis:
            return error_response("Analyse non trouvée", "analysis_not_found", 404)
        
        # Vérifier les permissions
        if analysis['user_id'] != user_id:
            # TODO: Vérifier si l'utilisateur est admin
            pass
        
        # Si le rapport est déjà stocké, le retourner
        if analysis.get('report_data'):
            return jsonify(success_response({
                'analysis_id': analysis_id,
                'status': analysis['status'],
                'report': analysis['report_data'],
                'generated_at': analysis['completed_at'].isoformat() if analysis.get('completed_at') else None,
                'file_info': analysis.get('file_info'),
                'scan_ids': analysis.get('scan_ids'),
                'full_report': analysis.get('analysis_type') == 'full'
            }))
        
        # Sinon, récupérer le rapport depuis le serveur IA
        try:
            response = requests.get(
                f"{EXTERNAL_AI_SERVER}/report/{analysis_id}",
                timeout=30
            )
            response.raise_for_status()
            ai_report = response.json()
            
            # Stocker le rapport dans MongoDB
            AnalysisManager.save_analysis_report(analysis_id, ai_report)
            
            return jsonify(success_response({
                'analysis_id': analysis_id,
                'status': 'completed',
                'report': ai_report,
                'generated_at': analysis['completed_at'].isoformat() if analysis.get('completed_at') else None,
                'file_info': analysis.get('file_info'),
                'scan_ids': analysis.get('scan_ids'),
                'full_report': analysis.get('analysis_type') == 'full'
            }))
            
        except requests.exceptions.RequestException as e:
            current_app.logger.error(f"Erreur lors de la récupération du rapport: {e}")
            return error_response("Erreur lors de la récupération du rapport depuis le serveur IA", str(e), 500)
        
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la récupération du rapport: {e}")
        return error_response("Erreur interne du serveur", str(e), 500)

@analysis_bp.route('/analysis/history', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_analysis_history():
    """
    Récupère l'historique des analyses de l'utilisateur.
    
    Query params:
        page: Numéro de page (défaut: 1)
        limit: Nombre d'éléments par page (défaut: 20)
        status: Filtre par statut (optionnel)
        
    Returns:
        JSON: Liste des analyses avec pagination
    """
    try:
        user_id = get_jwt_identity()
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        status_filter = request.args.get('status')
        
        result = AnalysisManager.get_user_analyses(
            user_id=user_id,
            page=page,
            limit=limit,
            status_filter=status_filter
        )
        
        return jsonify(success_response(result))
        
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la récupération de l'historique: {e}")
        return error_response("Erreur interne du serveur", str(e), 500)

@analysis_bp.route('/analysis/statistics', methods=['GET', 'OPTIONS'])
@handle_options
@jwt_required()
def get_analysis_statistics():
    """
    Récupère les statistiques des analyses de l'utilisateur.

    Returns:
        JSON: Statistiques des analyses
    """
    try:
        user_id = get_jwt_identity()
        stats = AnalysisManager.get_analysis_statistics(user_id)

        return jsonify(success_response(stats))

    except Exception as e:
        current_app.logger.error(f"Erreur lors de la récupération des statistiques: {e}")
        return error_response("Erreur interne du serveur", str(e), 500)

@analysis_bp.route('/analysis/cancel/<analysis_id>', methods=['POST', 'OPTIONS'])
@handle_options
@jwt_required()
def cancel_analysis(analysis_id):
    """
    Annule une analyse en cours.

    Args:
        analysis_id: ID de l'analyse à annuler

    Returns:
        JSON: Confirmation de l'annulation
    """
    try:
        user_id = get_jwt_identity()

        # Annuler l'analyse en base
        cancelled = AnalysisManager.cancel_analysis(analysis_id, user_id)

        if not cancelled:
            return error_response(
                "Impossible d'annuler l'analyse",
                "L'analyse n'existe pas, ne vous appartient pas, ou ne peut pas être annulée",
                400
            )

        # Essayer d'annuler sur le serveur IA externe (optionnel)
        try:
            from app.services.external_analytics_api import ExternalAnalyticsAPI
            external_api = ExternalAnalyticsAPI()

            # Tenter d'arrêter l'analyse sur le serveur externe
            # Note: Le serveur IA n'a peut-être pas d'endpoint cancel, donc on ignore les erreurs
            try:
                external_api.post(f'/cancel/{analysis_id}', {})
                current_app.logger.info(f"Analyse {analysis_id} annulée sur le serveur IA externe")
            except:
                current_app.logger.warning(f"Impossible d'annuler l'analyse {analysis_id} sur le serveur IA externe (endpoint peut-être non disponible)")

        except Exception as external_error:
            current_app.logger.warning(f"Erreur lors de l'annulation externe: {external_error}")
            # On continue même si l'annulation externe échoue

        current_app.logger.info(f"Analyse {analysis_id} annulée par l'utilisateur {user_id}")

        return jsonify(success_response({
            'analysis_id': analysis_id,
            'status': 'cancelled',
            'message': 'Analyse annulée avec succès'
        }))

    except Exception as e:
        current_app.logger.error(f"Erreur lors de l'annulation de l'analyse: {e}")
        return error_response("Erreur interne du serveur", str(e), 500)

@analysis_bp.route('/analysis/delete/<analysis_id>', methods=['DELETE', 'OPTIONS'])
@handle_options
@jwt_required()
def delete_analysis(analysis_id):
    """
    Supprime une analyse de l'utilisateur.
    
    Args:
        analysis_id: ID de l'analyse
        
    Returns:
        JSON: Confirmation de suppression
    """
    try:
        user_id = get_jwt_identity()
        
        success = AnalysisManager.delete_analysis(analysis_id, user_id)
        
        if success:
            return jsonify(success_response({'message': 'Analyse supprimée avec succès'}))
        else:
            return error_response("Analyse non trouvée ou non autorisée", "not_found_or_unauthorized", 404)
        
    except Exception as e:
        current_app.logger.error(f"Erreur lors de la suppression: {e}")
        return error_response("Erreur interne du serveur", str(e), 500)
