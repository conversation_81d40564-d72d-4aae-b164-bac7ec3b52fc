import { useAuthContext } from '../contexts/AuthContext';
import { authService } from '../services/authService';

export const useAuth = () => {
  const {
    user,
    token,
    role,
    timeRemaining,
    isAuthenticated,
    loading,
    login: contextLogin,
    logout,
    tryRefreshToken,
  } = useAuthContext();

  const login = async (email: string, password: string, rememberMe: boolean = false) => {
    try {
      const response = await authService.login({ email, password, remember_me: rememberMe });

      if (response.data) {
        const { token, refresh_token, user } = response.data;
        contextLogin(token, user, refresh_token, rememberMe);
        return { success: true, user };
      } else {
        return { success: false, message: 'Connection error' };
      }
    } catch (error: any) {
      const message = error?.response?.data?.msg || error.message || 'Server connection error';
      return { success: false, message };
    }
  };

  const register = async (userData: any) => {
    try {
      const response = await authService.register(userData);
      return { success: true, message: response.data.msg || 'Registration successful' };
    } catch (error: any) {
      const message = error?.response?.data?.msg || error.message || 'Registration error';
      return { success: false, message };
    }
  };

  return {
    user,
    token,
    role,
    timeRemaining,
    isAuthenticated,
    loading,
    login,
    logout,
    register,
    // Alias pour compatibilité
    isLoggedIn: isAuthenticated,
    checkAuthStatus: () => {}, // Maintenant géré par le contexte
  };
};
