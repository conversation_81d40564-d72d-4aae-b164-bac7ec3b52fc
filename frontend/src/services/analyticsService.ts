import { api } from './api';
import { externalAnalyticsApi } from './externalAnalyticsApi';
import { activeConfig } from '../config/api';

// Types pour les analyses
export interface AnalysisRequest {
  file?: File;
  scan_ids?: string;
  generate_full_report?: boolean;
}

export interface AnalysisResponse {
  analysis_id: string;
  status: string;
  message: string;
}

export interface AnalysisStatus {
  analysis_id: string;
  status: 'pending' | 'running' | 'processing' | 'completed' | 'failed' | 'error';
  progress: number;
  progress_precise?: number;
  message?: string;
  elapsed_time?: string;
  processing_speed?: number;
  estimated_time_remaining?: string;
  processed_vulnerabilities?: number;
  total_vulnerabilities?: number;
  started_at?: string;
  completed_at?: string;
  created_at?: string;
  filename?: string;
  error?: string;
}

export interface VulnerabilityInfo {
  basic_info: {
    scan_id: string;
    target: string;
    name: string;
    description: string;
    severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    cve: string;
    port: string;
    protocol: string;
    service: string;
    version: string;
    cvss_score: string;
    risk_level: string;
    solution: string;
    additional_details: string;
    scan_category: string;
    scan_type: string;
    tools_used: string;
    security_score: string;
    start_date: string;
    end_date: string;
  };
  ai_analysis: {
    vulnerability_analysis: {
      summary: string;
      technical_description: string;
      impact_assessment: {
        confidentiality: string;
        integrity: string;
        availability: string;
        business_impact: string;
      };
      exploitation_details: {
        attack_vector: string;
        prerequisites: string;
        difficulty: string;
        likelihood: string;
      };
      remediation: {
        immediate_actions: string[];
        long_term_solutions: string[];
        workarounds: string[];
        priority: string;
        estimated_effort: string;
      };
      responsibility_assignment: {
        primary_responsible: string;
        secondary_responsible: string;
        escalation_contact: string;
        required_skills: string[];
        required_access: string[];
        coordination_needed: string[];
      };
      prevention_strategy: {
        root_cause: string;
        prevention_measures: string[];
        monitoring_requirements: string[];
        policy_changes: string[];
        training_needs: string[];
        automated_controls: string[];
        regular_assessments: string[];
      };
      implementation_plan: {
        phase_1_immediate: {
          actions: string[];
          responsible: string;
          timeline: string;
        };
        phase_2_short_term: {
          actions: string[];
          responsible: string;
          timeline: string;
        };
        phase_3_long_term: {
          actions: string[];
          responsible: string;
          timeline: string;
        };
        verification_steps: string[];
        rollback_plan: string[];
      };
      references: string[];
      compliance_impact: string;
    };
  };
}

export interface ScanReport {
  scan_metadata: {
    scan_id: string;
    target: string;
    vulnerability_count: number;
    risk_levels: { [key: string]: number };
    severities: { [key: string]: number };
  };
  vulnerabilities: VulnerabilityInfo[];
}

export interface VulnerabilityAssessmentReport {
  vulnerability_assessment_report: {
    metadata: {
      total_scans: number;
      total_vulnerabilities: number;
      report_generated: string;
    };
    scan_reports: { [scanId: string]: ScanReport };
  };
}

export interface AnalysisReport {
  analysis_id: string;
  status: string;
  report: VulnerabilityAssessmentReport;
  generated_at: string;
  file_info?: {
    filename: string;
    size: number;
  };
  scan_ids?: string[];
  full_report?: boolean;
}

export interface AnalysisListItem {
  analysis_id: string;
  status: string;
  created_at: string;
  completed_at?: string;
  file_info?: {
    filename: string;
    size: number;
  };
  scan_ids?: string[];
  generate_full_report: boolean;
}

export interface AnalysisListResponse {
  analyses: AnalysisListItem[];
  total: number;
  page: number;
  limit: number;
}

// Fonction utilitaire pour mapper les statuts du serveur IA vers les statuts internes
function mapExternalStatus(externalStatus: string): string {
  switch (externalStatus) {
    case 'success':
      return 'completed';
    case 'running':
    case 'processing':
      return 'processing'; // Garder 'processing' pour plus de précision
    case 'pending':
    case 'queued':
      return 'pending';
    case 'failed':
    case 'error':
      return 'failed';
    default:
      return externalStatus;
  }
}

// Service pour les analyses
export const analyticsService = {
  /**
   * Lance une analyse complète avec IA (Full Report) directement sur le serveur externe
   * puis stocke les informations dans MongoDB via le backend
   */
  async startFullAnalysis(file: File): Promise<AnalysisResponse> {
    try {
      // 1. Lancer l'analyse sur le serveur IA externe
      const formData = new FormData();
      formData.append('file', file);
      formData.append('generate_full_report', 'true');

      const aiResponse = await externalAnalyticsApi.postFormData(
        activeConfig.ANALYTICS_ENDPOINTS.ANALYZE,
        formData,
        true // Afficher la progression
      );

      // 2. Stocker les informations dans MongoDB via le backend (optionnel)
      if (aiResponse.analysis_id) {
        // Stocker en arrière-plan sans bloquer l'utilisateur
        setTimeout(async () => {
          try {
            await api.post('/api/analysis/store', {
              analysis_id: aiResponse.analysis_id,
              file_info: {
                filename: file.name,
                size: file.size,
                content_type: file.type,
              },
              analysis_type: 'full',
              scan_ids: [],
            });
            console.log('✅ Analyse stockée en base avec succès:', aiResponse.analysis_id);
          } catch (storageError) {
            console.warn(
              '⚠️ Stockage en base échoué (analyse continue sur le serveur IA):',
              storageError
            );
            // L'analyse continue sur le serveur IA même si le stockage local échoue
          }
        }, 100);
      }

      // 3. Stocker localement pour l'historique
      if (aiResponse.analysis_id) {
        this.storeAnalysisLocally({
          analysis_id: aiResponse.analysis_id,
          filename: file.name,
          size: file.size,
          analysis_type: 'full',
          status: 'pending',
          created_at: new Date().toISOString(),
          scan_ids: [],
        });
      }

      return aiResponse;
    } catch (error) {
      console.error("Erreur lors du lancement de l'analyse:", error);
      throw error;
    }
  },

  /**
   * Lance une analyse basique avec filtrage par scan IDs directement sur le serveur externe
   * puis stocke les informations dans MongoDB via le backend
   */
  async startBasicAnalysis(file: File, scanIds: string): Promise<AnalysisResponse> {
    try {
      // 1. Lancer l'analyse sur le serveur IA externe
      const formData = new FormData();
      formData.append('file', file);
      formData.append('scan_ids', scanIds);
      formData.append('generate_full_report', 'false');

      const aiResponse = await externalAnalyticsApi.postFormData(
        activeConfig.ANALYTICS_ENDPOINTS.ANALYZE,
        formData,
        true // Afficher la progression
      );

      // 2. Stocker les informations dans MongoDB via le backend (optionnel)
      if (aiResponse.analysis_id) {
        // Stocker en arrière-plan sans bloquer l'utilisateur
        setTimeout(async () => {
          try {
            await api.post('/api/analysis/store', {
              analysis_id: aiResponse.analysis_id,
              file_info: {
                filename: file.name,
                size: file.size,
                content_type: file.type,
              },
              analysis_type: 'basic',
              scan_ids: scanIds.split(',').map((id) => id.trim()),
            });
            console.log('✅ Analyse stockée en base avec succès:', aiResponse.analysis_id);
          } catch (storageError) {
            console.warn(
              '⚠️ Stockage en base échoué (analyse continue sur le serveur IA):',
              storageError
            );
            // L'analyse continue sur le serveur IA même si le stockage local échoue
          }
        }, 100);
      }

      // 3. Stocker localement pour l'historique
      if (aiResponse.analysis_id) {
        this.storeAnalysisLocally({
          analysis_id: aiResponse.analysis_id,
          filename: file.name,
          size: file.size,
          analysis_type: 'basic',
          status: 'pending',
          created_at: new Date().toISOString(),
          scan_ids: scanIds.split(',').map((id) => id.trim()),
        });
      }

      return aiResponse;
    } catch (error) {
      console.error("Erreur lors du lancement de l'analyse:", error);
      throw error;
    }
  },

  /**
   * Vérifie le statut d'une analyse en cours directement sur le serveur IA
   * et met à jour la base de données via le backend
   */
  async getAnalysisStatus(analysisId: string): Promise<AnalysisStatus> {
    try {
      // 1. Interroger directement le serveur IA
      const aiResponse = await externalAnalyticsApi.get(
        `${activeConfig.ANALYTICS_ENDPOINTS.STATUS}/${analysisId}`
      );

      // Extraire les données d'analyse de la réponse
      const aiStatus = aiResponse.analysis || aiResponse;

      // Mapper le statut externe vers le statut interne
      const mappedStatus = mapExternalStatus(aiStatus.status);
      const mappedProgress =
        mappedStatus === 'completed' ? 100 : aiStatus.progress_precise || aiStatus.progress || 0;

      // Construire l'objet de statut enrichi
      const enrichedStatus: AnalysisStatus = {
        analysis_id: aiStatus.analysis_id || analysisId,
        status: mappedStatus,
        progress: Math.round(mappedProgress),
        progress_precise: aiStatus.progress_precise,
        message: aiStatus.message,
        elapsed_time: aiStatus.elapsed_time,
        processing_speed: aiStatus.processing_speed,
        estimated_time_remaining: aiStatus.estimated_time_remaining,
        processed_vulnerabilities: aiStatus.processed_vulnerabilities,
        total_vulnerabilities: aiStatus.total_vulnerabilities,
        created_at: aiStatus.created_at,
        filename: aiStatus.filename,
        started_at: aiStatus.started_at,
        completed_at: aiStatus.completed_at,
      };

      // 2. Mettre à jour la base de données via le backend (optionnel)
      try {
        await api.post('/api/analysis/update-status', {
          analysis_id: analysisId,
          status: mappedStatus,
          progress: enrichedStatus.progress,
          server_response: aiResponse,
        });
      } catch (updateError) {
        console.warn('Erreur lors de la mise à jour du statut en base:', updateError);
      }

      // 3. Mettre à jour le stockage local avec les données enrichies
      this.updateLocalAnalysisStatus(
        analysisId,
        mappedStatus,
        enrichedStatus.progress,
        enrichedStatus
      );

      return enrichedStatus;
    } catch (error) {
      // En cas d'erreur, essayer de récupérer depuis le backend
      try {
        const response = await api.get(`/api/analysis/status/${analysisId}`);
        return response.data;
      } catch (backendError) {
        console.error('Erreur lors de la récupération du statut:', error);
        throw error;
      }
    }
  },

  /**
   * Récupère le rapport final d'une analyse (priorité MongoDB, puis serveur IA)
   */
  async getAnalysisReport(analysisId: string): Promise<AnalysisReport> {
    try {
      // 1. Essayer d'abord de récupérer depuis MongoDB
      console.log('🔍 Tentative de récupération du rapport depuis MongoDB...');
      try {
        const response = await api.get(`/api/analysis/report/${analysisId}`);
        console.log('✅ Rapport récupéré depuis MongoDB:', response.data);
        return response.data;
      } catch (backendError) {
        console.warn('⚠️ Rapport non trouvé dans MongoDB, tentative via serveur IA externe...');
      }

      // 2. Fallback sur le serveur IA externe
      const aiReport = await externalAnalyticsApi.get(
        `${activeConfig.ANALYTICS_ENDPOINTS.REPORT}/${analysisId}`
      );

      // 3. Stocker le rapport dans MongoDB pour les prochaines fois
      try {
        await api.post('/api/analysis/save-report', {
          analysis_id: analysisId,
          report_data: aiReport,
        });
        console.log('✅ Rapport stocké dans MongoDB pour les prochaines fois');
      } catch (saveError) {
        console.warn('⚠️ Impossible de stocker le rapport dans MongoDB:', saveError);
      }

      return aiReport;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération du rapport:', error);
      throw error;
    }
  },

  /**
   * Récupère l'historique des analyses (priorité au stockage local, puis serveur IA)
   */
  async getAllAnalyses(
    page: number = 1,
    limit: number = 20,
    status?: string
  ): Promise<AnalysisListResponse> {
    try {
      // Essayer d'abord le backend local pour récupérer les analyses de la collection analyses_scan
      console.log('🔍 Tentative de récupération via backend local...');
      let url = `/api/analysis/history?page=${page}&limit=${limit}`;
      if (status) {
        url += `&status=${status}`;
      }
      const response = await api.get(url);
      console.log('✅ Analyses récupérées via backend local:', response.data);
      return response.data;
    } catch (backendError) {
      console.warn('⚠️ Backend local inaccessible, tentative via serveur IA externe...');

      try {
        // Fallback sur le serveur IA externe
        const aiResponse = await externalAnalyticsApi.get(
          activeConfig.ANALYTICS_ENDPOINTS.ANALYSES
        );

        // Le serveur IA retourne toutes les analyses, on fait la pagination côté client
        let allAnalyses = Array.isArray(aiResponse) ? aiResponse : aiResponse.analyses || [];

        // Filtrer par statut si demandé
        if (status && status !== 'all') {
          allAnalyses = allAnalyses.filter((analysis: any) => analysis.status === status);
        }

        // Pagination côté client
        const start = (page - 1) * limit;
        const end = start + limit;
        const paginatedAnalyses = allAnalyses.slice(start, end);

        return {
          analyses: paginatedAnalyses,
          total: allAnalyses.length,
          page: page,
          limit: limit,
          pages: Math.ceil(allAnalyses.length / limit),
        };
      } catch (aiError) {
        console.warn('⚠️ Serveur IA aussi inaccessible, utilisation des données locales');

        // Fallback final sur le stockage local
        return this.getLocalAnalyses(page, limit);
      }
    }
  },

  /**
   * Supprime une analyse de l'utilisateur
   */
  async deleteAnalysis(analysisId: string): Promise<{ message: string }> {
    const response = await api.delete(`/api/analysis/delete/${analysisId}`);
    return response.data;
  },

  /**
   * Annule une analyse en cours
   */
  async cancelAnalysis(analysisId: string): Promise<{ success: boolean; message: string }> {
    try {
      console.log(`🛑 Tentative d'annulation de l'analyse ${analysisId}...`);

      // Annuler via le backend local
      const response = await api.post(`/api/analysis/cancel/${analysisId}`, {});

      console.log(`✅ Analyse ${analysisId} annulée avec succès`);

      return {
        success: true,
        message: response.data.message || 'Analyse annulée avec succès'
      };

    } catch (error) {
      console.error(`❌ Erreur lors de l'annulation de l'analyse ${analysisId}:`, error);

      const errorMessage = error instanceof Error ? error.message : 'Erreur lors de l\'annulation';

      return {
        success: false,
        message: errorMessage
      };
    }
  },

  /**
   * Obtient les statistiques des analyses basées sur les données locales
   */
  async getAnalyticsStatistics(): Promise<{
    total_analyses: number;
    completed_analyses: number;
    failed_analyses: number;
    pending_analyses: number;
    running_analyses: number;
    full_reports: number;
    basic_reports: number;
  }> {
    try {
      // Essayer d'abord le backend local pour les statistiques
      console.log('🔍 Tentative de récupération des statistiques via backend local...');
      try {
        const response = await api.get('/api/analysis/statistics');
        console.log('✅ Statistiques récupérées via backend local:', response.data);
        return response.data;
      } catch (backendError) {
        console.warn(
          '⚠️ Backend local inaccessible pour les statistiques, utilisation des données locales'
        );
      }

      // Fallback sur les données locales
      const stored = localStorage.getItem('pica_analyses_history');
      const analyses = stored ? JSON.parse(stored) : [];

      const stats = {
        total_analyses: analyses.length,
        completed_analyses: 0,
        failed_analyses: 0,
        pending_analyses: 0,
        running_analyses: 0,
        full_reports: 0,
        basic_reports: 0,
      };

      analyses.forEach((analysis: any) => {
        // Compter par statut
        switch (analysis.status) {
          case 'completed':
            stats.completed_analyses++;
            break;
          case 'failed':
          case 'error':
            stats.failed_analyses++;
            break;
          case 'pending':
            stats.pending_analyses++;
            break;
          case 'running':
          case 'processing':
            stats.running_analyses++;
            break;
        }

        // Compter par type
        if (analysis.analysis_type === 'full') {
          stats.full_reports++;
        } else if (analysis.analysis_type === 'basic') {
          stats.basic_reports++;
        }
      });

      return stats;
    } catch (error) {
      console.warn('⚠️ Erreur lors du calcul des statistiques locales:', error);

      // Retourner des statistiques vides en cas d'erreur
      return {
        total_analyses: 0,
        completed_analyses: 0,
        failed_analyses: 0,
        pending_analyses: 0,
        running_analyses: 0,
        full_reports: 0,
        basic_reports: 0,
      };
    }
  },

  /**
   * Exporte les résultats d'analyse en CSV/JSON
   */
  async exportAnalyses(format: 'csv' | 'json' = 'csv'): Promise<Blob> {
    // Pour l'export, on peut avoir besoin d'une gestion spéciale du blob
    try {
      const response = await externalAnalyticsApi.get(
        `${activeConfig.ANALYTICS_ENDPOINTS.EXPORT}?format=${format}`
      );
      // Si la réponse est déjà un blob, la retourner directement
      if (response instanceof Blob) {
        return response;
      }
      // Sinon, créer un blob à partir des données
      return new Blob([JSON.stringify(response)], {
        type: format === 'csv' ? 'text/csv' : 'application/json',
      });
    } catch (error) {
      console.error("Erreur lors de l'export:", error);
      throw error;
    }
  },

  /**
   * Annule une analyse en cours
   */
  async cancelAnalysis(analysisId: string): Promise<{ message: string }> {
    return await externalAnalyticsApi.post(`/analyses/${analysisId}/cancel`);
  },

  /**
   * Relance une analyse échouée
   */
  async retryAnalysis(analysisId: string): Promise<AnalysisResponse> {
    return await externalAnalyticsApi.post(`/analyses/${analysisId}/retry`);
  },

  /**
   * Test de connectivité avec le serveur Analytics externe
   */
  async testConnection(
    skipCors: boolean = false
  ): Promise<{ success: boolean; message: string; latency?: number }> {
    return await externalAnalyticsApi.testConnection(skipCors);
  },

  /**
   * Stocke une analyse localement dans le localStorage
   */
  storeAnalysisLocally(analysis: {
    analysis_id: string;
    filename: string;
    size: number;
    analysis_type: string;
    status: string;
    created_at: string;
    scan_ids: string[];
  }): void {
    try {
      const stored = localStorage.getItem('pica_analyses_history');
      const analyses = stored ? JSON.parse(stored) : [];

      // Éviter les doublons
      const existingIndex = analyses.findIndex((a: any) => a.analysis_id === analysis.analysis_id);
      if (existingIndex >= 0) {
        analyses[existingIndex] = { ...analyses[existingIndex], ...analysis };
      } else {
        analyses.unshift(analysis); // Ajouter au début
      }

      // Limiter à 100 analyses max
      if (analyses.length > 100) {
        analyses.splice(100);
      }

      localStorage.setItem('pica_analyses_history', JSON.stringify(analyses));
      console.log('✅ Analyse stockée localement:', analysis.analysis_id);
    } catch (error) {
      console.warn('⚠️ Erreur lors du stockage local:', error);
    }
  },

  /**
   * Récupère les analyses stockées localement
   */
  getLocalAnalyses(page: number = 1, limit: number = 20): AnalysisListResponse {
    try {
      const stored = localStorage.getItem('pica_analyses_history');
      const allAnalyses = stored ? JSON.parse(stored) : [];

      const start = (page - 1) * limit;
      const end = start + limit;
      const analyses = allAnalyses.slice(start, end);

      return {
        analyses,
        total: allAnalyses.length,
        page,
        limit,
        pages: Math.ceil(allAnalyses.length / limit),
      };
    } catch (error) {
      console.warn('⚠️ Erreur lors de la récupération locale:', error);
      return {
        analyses: [],
        total: 0,
        page,
        limit,
        pages: 0,
      };
    }
  },

  /**
   * Met à jour le statut d'une analyse stockée localement
   */
  updateLocalAnalysisStatus(
    analysisId: string,
    status: string,
    progress?: number,
    enrichedData?: Partial<AnalysisStatus>
  ): void {
    try {
      const stored = localStorage.getItem('pica_analyses_history');
      if (!stored) return;

      const analyses = JSON.parse(stored);
      const index = analyses.findIndex((a: any) => a.analysis_id === analysisId);

      if (index >= 0) {
        // Mapper le statut avant de le stocker
        const mappedStatus = mapExternalStatus(status);
        analyses[index].status = mappedStatus;

        if (progress !== undefined) {
          analyses[index].progress = progress;
        }

        // Ajouter les données enrichies si disponibles
        if (enrichedData) {
          if (enrichedData.progress_precise !== undefined) {
            analyses[index].progress_precise = enrichedData.progress_precise;
          }
          if (enrichedData.message) {
            analyses[index].message = enrichedData.message;
          }
          if (enrichedData.elapsed_time) {
            analyses[index].elapsed_time = enrichedData.elapsed_time;
          }
          if (enrichedData.estimated_time_remaining) {
            analyses[index].estimated_time_remaining = enrichedData.estimated_time_remaining;
          }
          if (enrichedData.processed_vulnerabilities !== undefined) {
            analyses[index].processed_vulnerabilities = enrichedData.processed_vulnerabilities;
          }
          if (enrichedData.total_vulnerabilities !== undefined) {
            analyses[index].total_vulnerabilities = enrichedData.total_vulnerabilities;
          }
          if (enrichedData.processing_speed !== undefined) {
            analyses[index].processing_speed = enrichedData.processing_speed;
          }
          if (enrichedData.filename) {
            analyses[index].filename = enrichedData.filename;
          }
        }

        if (mappedStatus === 'completed') {
          analyses[index].completed_at = new Date().toISOString();
          analyses[index].progress = 100; // S'assurer que le progrès est à 100%
        }

        localStorage.setItem('pica_analyses_history', JSON.stringify(analyses));
        console.log(
          '✅ Statut local mis à jour:',
          analysisId,
          mappedStatus,
          enrichedData?.message || ''
        );
      }
    } catch (error) {
      console.warn('⚠️ Erreur lors de la mise à jour locale:', error);
    }
  },

  /**
   * Obtient les informations de configuration du serveur Analytics
   */
  getServerInfo(): { baseUrl: string; config: any } {
    return {
      baseUrl: externalAnalyticsApi.getBaseUrl(),
      config: externalAnalyticsApi.getConfig(),
    };
  },
};

// Utilitaires pour les analyses
export const analyticsUtils = {
  /**
   * Formate le statut d'analyse pour l'affichage
   */
  formatStatus(status: string): { text: string; color: string; bgColor: string } {
    switch (status.toLowerCase()) {
      case 'pending':
        return {
          text: 'Pending',
          color: 'text-yellow-400',
          bgColor: 'bg-yellow-500/20 border-yellow-500/30',
        };
      case 'running':
        return {
          text: 'Running',
          color: 'text-blue-400',
          bgColor: 'bg-blue-500/20 border-blue-500/30',
        };
      case 'processing':
        return {
          text: 'Processing',
          color: 'text-indigo-400',
          bgColor: 'bg-indigo-500/20 border-indigo-500/30',
        };
      case 'completed':
        return {
          text: 'Completed',
          color: 'text-green-400',
          bgColor: 'bg-green-500/20 border-green-500/30',
        };
      case 'failed':
      case 'error':
        return {
          text: 'Failed',
          color: 'text-red-400',
          bgColor: 'bg-red-500/20 border-red-500/30',
        };
      default:
        return {
          text: status,
          color: 'text-gray-400',
          bgColor: 'bg-gray-500/20 border-gray-500/30',
        };
    }
  },

  /**
   * Calcule la durée d'une analyse
   */
  calculateDuration(startTime: string, endTime?: string): string {
    const start = new Date(startTime);
    const end = endTime ? new Date(endTime) : new Date();
    const diffMs = end.getTime() - start.getTime();

    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);

    if (hours > 0) {
      return `${hours}h ${minutes}m ${seconds}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${seconds}s`;
    } else {
      return `${seconds}s`;
    }
  },

  /**
   * Formate la taille de fichier
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  /**
   * Valide un fichier CSV
   */
  validateCSVFile(file: File): { isValid: boolean; error?: string } {
    if (!file) {
      return { isValid: false, error: 'Aucun fichier sélectionné' };
    }

    if (!file.name.toLowerCase().endsWith('.csv')) {
      return { isValid: false, error: 'Le fichier doit être au format CSV' };
    }

    if (file.size > 50 * 1024 * 1024) {
      // 50MB max
      return { isValid: false, error: 'Le fichier ne doit pas dépasser 50MB' };
    }

    return { isValid: true };
  },

  /**
   * Parse les scan IDs depuis une chaîne
   */
  parseScanIds(scanIds: string): string[] {
    return scanIds
      .split(',')
      .map((id) => id.trim())
      .filter((id) => id.length > 0);
  },

  /**
   * Valide les scan IDs
   */
  validateScanIds(scanIds: string): { isValid: boolean; error?: string } {
    if (!scanIds || scanIds.trim().length === 0) {
      return { isValid: false, error: 'Les scan IDs sont requis' };
    }

    const ids = this.parseScanIds(scanIds);
    if (ids.length === 0) {
      return { isValid: false, error: 'Au moins un scan ID valide est requis' };
    }

    // Validation basique du format des IDs
    const invalidIds = ids.filter((id) => !/^[a-zA-Z0-9_-]+$/.test(id));
    if (invalidIds.length > 0) {
      return {
        isValid: false,
        error: `IDs invalides: ${invalidIds.join(', ')}`,
      };
    }

    return { isValid: true };
  },

  /**
   * Formats severity for display
   */
  formatSeverity(severity: string): { text: string; color: string; bgColor: string; icon: string } {
    const safeSeverity = (severity || 'unknown').toUpperCase();
    switch (safeSeverity) {
      case 'CRITICAL':
        return {
          text: 'Critical',
          color: 'text-red-400',
          bgColor: 'bg-red-500/20 border-red-500/30',
          icon: '🔴',
        };
      case 'HIGH':
        return {
          text: 'High',
          color: 'text-orange-400',
          bgColor: 'bg-orange-500/20 border-orange-500/30',
          icon: '🟠',
        };
      case 'MEDIUM':
        return {
          text: 'Medium',
          color: 'text-yellow-400',
          bgColor: 'bg-yellow-500/20 border-yellow-500/30',
          icon: '🟡',
        };
      case 'LOW':
        return {
          text: 'Low',
          color: 'text-green-400',
          bgColor: 'bg-green-500/20 border-green-500/30',
          icon: '🟢',
        };
      default:
        return {
          text: severity,
          color: 'text-gray-400',
          bgColor: 'bg-gray-500/20 border-gray-500/30',
          icon: '⚪',
        };
    }
  },

  /**
   * Formats remediation priority
   */
  formatPriority(priority: string): { text: string; color: string; bgColor: string } {
    const safePriority = (priority || 'medium').toLowerCase();
    switch (safePriority) {
      case 'high':
        return {
          text: 'High',
          color: 'text-red-400',
          bgColor: 'bg-red-500/20 border-red-500/30',
        };
      case 'medium':
        return {
          text: 'Medium',
          color: 'text-yellow-400',
          bgColor: 'bg-yellow-500/20 border-yellow-500/30',
        };
      case 'low':
        return {
          text: 'Low',
          color: 'text-green-400',
          bgColor: 'bg-green-500/20 border-green-500/30',
        };
      default:
        return {
          text: priority,
          color: 'text-gray-400',
          bgColor: 'bg-gray-500/20 border-gray-500/30',
        };
    }
  },

  /**
   * Extrait les vulnérabilités de toutes les analyses
   */
  extractVulnerabilities(report: any): VulnerabilityInfo[] {
    const vulnerabilities: VulnerabilityInfo[] = [];

    console.log('🔍 Extraction des vulnérabilités depuis le rapport:', report);

    // Gérer différentes structures de rapport
    let assessmentReport = null;

    // Structure 1: report.vulnerability_assessment_report
    if (report?.vulnerability_assessment_report) {
      assessmentReport = report.vulnerability_assessment_report;
      console.log('📍 Structure 1 détectée: report.vulnerability_assessment_report');
    }
    // Structure 2: report.report.vulnerability_assessment_report
    else if (report?.report?.vulnerability_assessment_report) {
      assessmentReport = report.report.vulnerability_assessment_report;
      console.log('📍 Structure 2 détectée: report.report.vulnerability_assessment_report');
    }
    // Structure 3: report.report.report.vulnerability_assessment_report
    else if (report?.report?.report?.vulnerability_assessment_report) {
      assessmentReport = report.report.report.vulnerability_assessment_report;
      console.log('📍 Structure 3 détectée: report.report.report.vulnerability_assessment_report');
    }

    if (assessmentReport?.scan_reports) {
      console.log('📊 Scan reports trouvés:', Object.keys(assessmentReport.scan_reports));

      Object.values(assessmentReport.scan_reports).forEach((scanReport: any) => {
        if (scanReport.vulnerabilities && Array.isArray(scanReport.vulnerabilities)) {
          console.log(`🔍 ${scanReport.vulnerabilities.length} vulnérabilités trouvées dans le scan`);
          vulnerabilities.push(...scanReport.vulnerabilities);
        }
      });
    } else {
      console.log('❌ Aucun scan_reports trouvé. Structure du rapport:', {
        hasVulnerabilityAssessmentReport: !!assessmentReport,
        reportKeys: assessmentReport ? Object.keys(assessmentReport) : 'N/A',
        fullReportKeys: Object.keys(report),
        reportReportKeys: report.report ? Object.keys(report.report) : 'N/A'
      });
    }

    console.log(`✅ Total des vulnérabilités extraites: ${vulnerabilities.length}`);
    return vulnerabilities;
  },

  /**
   * Groupe les vulnérabilités par sévérité
   */
  groupVulnerabilitiesBySeverity(vulnerabilities: VulnerabilityInfo[]): {
    [severity: string]: VulnerabilityInfo[];
  } {
    return vulnerabilities.reduce(
      (groups, vuln) => {
        const severity = vuln.basic_info?.severity || 'unknown';
        if (!groups[severity]) {
          groups[severity] = [];
        }
        groups[severity].push(vuln);
        return groups;
      },
      {} as { [severity: string]: VulnerabilityInfo[] }
    );
  },

  /**
   * Calcule les statistiques des vulnérabilités
   */
  calculateVulnerabilityStats(vulnerabilities: VulnerabilityInfo[]) {
    const stats = {
      total: vulnerabilities.length,
      critical: 0,
      high: 0,
      medium: 0,
      low: 0,
      withCVE: 0,
      targets: new Set<string>(),
      scanIds: new Set<string>(),
    };

    vulnerabilities.forEach((vuln) => {
      // Safe access to basic_info properties
      const basicInfo = vuln.basic_info || {};
      const severity = (basicInfo.severity || 'unknown').toUpperCase();

      switch (severity) {
        case 'CRITICAL':
          stats.critical++;
          break;
        case 'HIGH':
          stats.high++;
          break;
        case 'MEDIUM':
          stats.medium++;
          break;
        case 'LOW':
          stats.low++;
          break;
      }

      if (basicInfo.cve) {
        stats.withCVE++;
      }

      if (basicInfo.target) {
        stats.targets.add(basicInfo.target);
      }
      if (basicInfo.scan_id) {
        stats.scanIds.add(basicInfo.scan_id);
      }
    });

    return {
      ...stats,
      uniqueTargets: stats.targets.size,
      uniqueScans: stats.scanIds.size,
    };
  },
};
