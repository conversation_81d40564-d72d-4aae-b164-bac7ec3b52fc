import { api } from './api';

export interface CreateUserData {
  first_name: string;
  last_name: string;
  date_of_birth: string;
  username: string;
  gender: string;
  email: string;
  password: string;
  role?: 'user' | 'admin';
}

export interface UpdateUserData {
  first_name?: string;
  last_name?: string;
  date_of_birth?: string;
  username?: string;
  gender?: string;
  email?: string;
  password?: string;
  role?: 'user' | 'admin';
}

export const userService = {
  // Get all users
  getAll: () => api.get('/admin/users'),

  // Get user by ID
  getById: (id: string) => api.get(`/admin/users/${id}`),

  // Create new user
  create: (userData: CreateUserData) => api.post('/admin/users', userData),

  // Update user
  update: (id: string, userData: UpdateUserData) => api.put(`/admin/users/${id}`, userData),

  // Delete user
  delete: (id: string) => api.delete(`/admin/users/${id}`),

  // Ban user
  ban: (id: string) => api.put(`/admin/users/${id}/ban`),

  // Unban user
  unban: (id: string) => api.put(`/admin/users/${id}/unban`),

  // Legacy method for role change (keeping for compatibility)
  changeRole: (id: string, role: string) => api.put(`/admin/users/${id}`, { role }),

  // Session management
  getUserSessions: (id: string, limit?: number, activeOnly?: boolean) => {
    const params = new URLSearchParams();
    if (limit) params.append('limit', limit.toString());
    if (activeOnly) params.append('active_only', 'true');
    return api.get(`/admin/users/${id}/sessions?${params.toString()}`);
  },

  getUserSessionStats: (id: string) => api.get(`/admin/users/${id}/sessions/stats`),

  endAllUserSessions: (id: string) => api.post(`/admin/users/${id}/sessions/end-all`),

  endSpecificSession: (sessionId: string) => api.post(`/admin/sessions/${sessionId}/end`),
};
