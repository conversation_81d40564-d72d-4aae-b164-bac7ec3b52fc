import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { addAlert } from '../store/slices/alertSlice';
import {
  getToken,
  getUser,
  getRole,
  getRefreshToken,
  setToken as saveToken,
  setRefreshToken as saveRefreshToken,
  setUser as saveUser,
  setRole as saveRole,
  clearAuthData,
  isTokenExpired,
  getTokenTimeRemaining,
  refreshAccessToken,
  debugAuthStatus,
} from '../utils/auth';
import { api } from '../services/api';

interface User {
  id?: string;
  username?: string;
  email?: string;
  role?: string;
  [key: string]: any;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  role: string | null;
  timeRemaining: number;
  isAuthenticated: boolean;
  loading: boolean;
  login: (token: string, user: User, refreshToken?: string, rememberMe?: boolean) => void;
  logout: () => void;
  tryRefreshToken: () => Promise<boolean>;
  checkSessionValidity: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType>({} as AuthContextType);

export const useAuthContext = () => {
  const context = useContext(AuthContext);
  if (!context) throw new Error('useAuthContext must be used within an AuthProvider');
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider = ({ children }: AuthProviderProps) => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [role, setRole] = useState<string | null>(null);
  const [timeRemaining, setTimeRemaining] = useState<number>(7200);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [hasRememberMe, setHasRememberMe] = useState<boolean>(false);
  const [sessionWarningShown, setSessionWarningShown] = useState<boolean>(false);
  const [sessionCheckInterval, setSessionCheckInterval] = useState<NodeJS.Timeout | null>(null);

  // Function to check if session is still valid
  const checkSessionValidity = async (): Promise<boolean> => {
    try {
      // Make a simple API call to check if session is still valid
      const response = await api.get('/auth/verify-session');
      return true;
    } catch (error: any) {
      if (error.response?.data?.code === 'SESSION_TERMINATED') {
        console.log('🚨 Session terminated by administrator - auto logout');
        dispatch(
          addAlert({
            id: Date.now().toString(),
            type: 'warning',
            message: 'Votre session a été terminée par un administrateur.',
            duration: 5000,
          })
        );
        logout();
        return false;
      }

      if (error.response?.data?.code === 'ACCOUNT_BANNED') {
        console.log('🚨 Account has been banned - auto logout');
        dispatch(
          addAlert({
            id: Date.now().toString(),
            type: 'error',
            message: 'Votre compte a été banni par un administrateur.',
            duration: 5000,
          })
        );
        logout();
        return false;
      }

      if (error.response?.data?.code === 'ACCOUNT_DELETED') {
        console.log('🚨 Account has been deleted - auto logout');
        dispatch(
          addAlert({
            id: Date.now().toString(),
            type: 'error',
            message: 'Votre compte a été supprimé par un administrateur.',
            duration: 5000,
          })
        );
        logout();
        return false;
      }

      // For other errors, don't force logout (might be network issues)
      return true;
    }
  };

  // Start periodic session checking
  const startSessionChecking = () => {
    if (sessionCheckInterval) {
      clearInterval(sessionCheckInterval);
    }

    const interval = setInterval(async () => {
      if (isAuthenticated && token) {
        await checkSessionValidity();
      }
    }, 30000); // Check every 30 seconds

    setSessionCheckInterval(interval);
  };

  // Stop session checking
  const stopSessionChecking = () => {
    if (sessionCheckInterval) {
      clearInterval(sessionCheckInterval);
      setSessionCheckInterval(null);
    }
  };

  const tryRefreshToken = async (): Promise<boolean> => {
    try {
      const refreshResult = await refreshAccessToken();

      if (refreshResult.success && refreshResult.token && refreshResult.user) {
        // Mettre à jour le token et l'utilisateur
        setToken(refreshResult.token);
        setUser(refreshResult.user);
        setRole(refreshResult.user.role);
        setSessionWarningShown(false); // Reset session warning after successful refresh

        // Calculer le nouveau temps restant
        const remaining = getTokenTimeRemaining(refreshResult.token);
        setTimeRemaining(Math.floor(remaining));

        // Sauvegarder le nouveau token
        saveToken(refreshResult.token, false); // Ne pas étendre l'expiration du cookie

        console.log('✅ Token refreshed successfully');
        return true;
      } else {
        console.log('❌ Token refresh failed:', refreshResult.message);
        return false;
      }
    } catch (error) {
      console.error('❌ Error during token refresh:', error);
      return false;
    }
  };

  useEffect(() => {
    const initializeAuth = async () => {
      const storedToken = getToken();
      const storedUser = getUser();
      const storedRole = getRole();
      const storedRefreshToken = getRefreshToken();

      // Ajouter la fonction de debug au window global
      (window as any).debugAuth = debugAuthStatus;

      if (storedToken && storedUser && storedRole) {
        // Vérifier si le token n'est pas expiré
        if (!isTokenExpired(storedToken)) {
          setToken(storedToken);
          setUser(storedUser);
          setRole(storedRole);
          setIsAuthenticated(true);

          // Start session checking
          startSessionChecking();

          // Calculer le temps restant basé sur le token réel
          const remaining = getTokenTimeRemaining(storedToken);
          setTimeRemaining(Math.floor(remaining));
        } else {
          // Token expiré, essayer de le rafraîchir si on a un refresh token
          if (storedRefreshToken && !isTokenExpired(storedRefreshToken)) {
            console.log('🔄 Access token expired on init, attempting refresh...');
            const refreshSuccess = await tryRefreshToken();
            if (!refreshSuccess) {
              console.log('🔒 Failed to refresh token on init, clearing auth data');
              clearAuthData();
              setIsAuthenticated(false);
            }
          } else {
            // Pas de refresh token valide, nettoyer les données
            console.log('🔒 No valid refresh token, clearing auth data');
            clearAuthData();
            setIsAuthenticated(false);
          }
        }
      } else if (storedUser && storedRole) {
        // Si on a un utilisateur mais pas de token d'accès, essayer de rafraîchir
        if (storedRefreshToken && !isTokenExpired(storedRefreshToken)) {
          console.log('🔄 No access token but refresh token available, attempting refresh...');
          const refreshSuccess = await tryRefreshToken();
          if (!refreshSuccess) {
            console.log('🔒 Failed to refresh token, logging out automatically');
            clearAuthData();
            setIsAuthenticated(false);
            navigate('/auth/login');
          }
        } else {
          // Pas de refresh token valide, déconnecter automatiquement
          console.log('🔒 No access token and no valid refresh token, logging out automatically');
          clearAuthData();
          setIsAuthenticated(false);
          navigate('/auth/login');
        }
      } else if (storedRefreshToken && !isTokenExpired(storedRefreshToken)) {
        // Seulement un refresh token, essayer de récupérer l'accès
        console.log('🔄 Only refresh token available, attempting to restore session...');
        const refreshSuccess = await tryRefreshToken();
        if (!refreshSuccess) {
          console.log('🔒 Failed to restore session, clearing refresh token');
          clearAuthData();
          setIsAuthenticated(false);
        }
      } else {
        // Aucune donnée d'authentification valide
        setIsAuthenticated(false);
      }
      setLoading(false);
    };

    initializeAuth();
  }, [navigate]);

  useEffect(() => {
    const interval = setInterval(async () => {
      const currentToken = getToken();

      if (currentToken && !isTokenExpired(currentToken)) {
        // Mettre à jour le temps restant basé sur le token réel
        const remaining = getTokenTimeRemaining(currentToken);
        setTimeRemaining(Math.floor(remaining));

        // La notification d'expiration est maintenant gérée par SessionExpirationWarning component

        // Si le token expire dans moins de 5 minutes ET que l'utilisateur n'a pas "Remember Me", essayer de le rafraîchir
        if (remaining < 300 && remaining > 0 && !hasRememberMe) {
          console.log('🔄 Token expires soon, attempting refresh...');
          const refreshSuccess = await tryRefreshToken();
          if (!refreshSuccess) {
            console.log('🔒 Failed to refresh token, will logout when expired');
          }
        }
      } else if (currentToken && isTokenExpired(currentToken)) {
        // Token expiré, essayer de le rafraîchir avant de déconnecter SEULEMENT si pas "Remember Me"
        if (!hasRememberMe) {
          console.log('🔄 Token expired, attempting refresh...');
          const refreshSuccess = await tryRefreshToken();
          if (!refreshSuccess) {
            console.log('🔒 Token expired and refresh failed, logging out automatically');
            logout();
          }
        } else {
          console.log('🔒 Token expired with Remember Me, logging out automatically');
          logout();
        }
      } else if (isAuthenticated) {
        // Pas de token mais utilisateur authentifié, déconnecter immédiatement
        console.log(
          '🔒 No access token found but user is authenticated, logging out automatically'
        );
        logout();
      } else {
        // Pas de token, décompte simple
        setTimeRemaining((prev) => (prev > 0 ? prev - 1 : 0));
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [hasRememberMe, isAuthenticated, sessionWarningShown, dispatch]);

  const login = (
    newToken: string,
    newUser: User,
    refreshToken?: string,
    rememberMe: boolean = false
  ) => {
    setToken(newToken);
    setUser(newUser);
    const userRole = newUser?.role || 'user';
    setRole(userRole);
    setIsAuthenticated(true);
    setHasRememberMe(rememberMe);
    setSessionWarningShown(false); // Reset session warning for new login

    // Start session checking
    startSessionChecking();

    // Calculer le temps restant basé sur le nouveau token
    const remaining = getTokenTimeRemaining(newToken);
    setTimeRemaining(Math.floor(remaining));

    // Sauvegarder dans les cookies au lieu du localStorage
    saveToken(newToken, rememberMe);
    if (refreshToken && !rememberMe) {
      // Sauvegarder le refresh token seulement si pas "Remember Me"
      saveRefreshToken(refreshToken, rememberMe);
      console.log('🔄 Refresh token saved for active session management');
    } else if (rememberMe) {
      console.log('⏰ Remember Me enabled - no refresh token needed (24h session)');
    }
    saveUser(newUser, rememberMe);
    saveRole(userRole, rememberMe);
  };

  const logout = () => {
    // Stop session checking
    stopSessionChecking();

    // Nettoyer toutes les données d'authentification des cookies
    clearAuthData();

    // Nettoyer le localStorage au cas où il y aurait encore des données
    localStorage.clear();

    // Réinitialiser l'état
    setToken(null);
    setUser(null);
    setRole(null);
    setIsAuthenticated(false);

    // Redirect to login page
    navigate('/login');
  };

  // Check session when window gains focus (user returns to tab)
  useEffect(() => {
    const handleFocus = async () => {
      if (isAuthenticated && token) {
        await checkSessionValidity();
      }
    };

    const handleClick = async () => {
      if (isAuthenticated && token) {
        // Throttle clicks to avoid too many requests
        const lastCheck = sessionStorage.getItem('lastSessionCheck');
        const now = Date.now();
        if (!lastCheck || now - parseInt(lastCheck) > 10000) {
          // Check at most every 10 seconds
          sessionStorage.setItem('lastSessionCheck', now.toString());
          await checkSessionValidity();
        }
      }
    };

    window.addEventListener('focus', handleFocus);
    document.addEventListener('click', handleClick);

    return () => {
      window.removeEventListener('focus', handleFocus);
      document.removeEventListener('click', handleClick);
    };
  }, [isAuthenticated, token]);

  // Cleanup interval on unmount
  useEffect(() => {
    return () => {
      stopSessionChecking();
    };
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        token,
        role,
        timeRemaining,
        isAuthenticated,
        loading,
        login,
        logout,
        tryRefreshToken,
        checkSessionValidity,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};
