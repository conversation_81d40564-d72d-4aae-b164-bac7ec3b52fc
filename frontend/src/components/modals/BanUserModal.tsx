import { useState } from 'react';
import { X, Ban, AlertTriangle, UserX, Shield } from 'lucide-react';

interface BanUserModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  username: string;
  loading?: boolean;
}

export default function BanUserModal({
  isOpen,
  onClose,
  onConfirm,
  username,
  loading = false,
}: BanUserModalProps) {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-red-500/30 rounded-xl max-w-md w-full shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-red-600/20 rounded-full flex items-center justify-center">
              <Ban size={20} className="text-red-400" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">Bannir l'utilisateur</h2>
              <p className="text-gray-400 text-sm">Action de modération</p>
            </div>
          </div>

          <button
            onClick={onClose}
            disabled={loading}
            className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-4">
          {/* Warning Icon */}
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center">
              <AlertTriangle size={32} className="text-red-400" />
            </div>
          </div>

          {/* Main Message */}
          <div className="text-center">
            <h3 className="text-lg font-semibold text-white mb-2">
              Êtes-vous sûr de vouloir bannir <span className="text-red-400">@{username}</span> ?
            </h3>
            <p className="text-gray-400 text-sm">Cette action aura les conséquences suivantes :</p>
          </div>

          {/* Consequences List */}
          <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <UserX size={16} className="text-red-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-red-300 font-medium text-sm">Accès bloqué immédiatement</p>
                  <p className="text-red-400/80 text-xs">
                    L'utilisateur ne pourra plus se connecter
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Shield size={16} className="text-red-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-red-300 font-medium text-sm">Sessions terminées</p>
                  <p className="text-red-400/80 text-xs">
                    Toutes les sessions actives seront fermées
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <Ban size={16} className="text-red-400 mt-0.5 flex-shrink-0" />
                <div>
                  <p className="text-red-300 font-medium text-sm">
                    Accès aux fonctionnalités bloqué
                  </p>
                  <p className="text-red-400/80 text-xs">
                    Aucune interaction possible avec la plateforme
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Reversible Notice */}
          <div className="bg-blue-900/20 border border-blue-500/30 rounded-lg p-3">
            <div className="flex items-center space-x-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <p className="text-blue-300 text-sm">
                <span className="font-medium">Note :</span> Cette action peut être annulée en
                débannissant l'utilisateur plus tard.
              </p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-700 bg-gray-800/50">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-gray-400 hover:text-white border border-gray-600 rounded-lg hover:bg-gray-700 transition-colors disabled:opacity-50"
          >
            Annuler
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            className="bg-red-600 hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : (
              <Ban size={16} />
            )}
            <span>{loading ? 'Bannissement...' : "Bannir l'utilisateur"}</span>
          </button>
        </div>
      </div>
    </div>
  );
}
