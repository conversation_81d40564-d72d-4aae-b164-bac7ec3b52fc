import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, Loader } from 'lucide-react';
import { analyticsService } from '../../services/analyticsService';
import { useNotifications } from '../../contexts/NotificationContext';

interface CancelAnalysisButtonProps {
  analysisId: string;
  analysisType?: string;
  onCancelled?: () => void;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'button' | 'icon';
}

const CancelAnalysisButton: React.FC<CancelAnalysisButtonProps> = ({
  analysisId,
  analysisType = 'analyse',
  onCancelled,
  disabled = false,
  size = 'md',
  variant = 'button'
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);
  const { showSuccess, showError } = useNotifications();

  const handleCancel = async () => {
    if (isLoading) return;

    setIsLoading(true);
    
    try {
      const result = await analyticsService.cancelAnalysis(analysisId);
      
      if (result.success) {
        showSuccess(
          result.message || `${analysisType} annulée avec succès`,
          'Annulation réussie'
        );
        
        // Fermer la confirmation
        setShowConfirm(false);
        
        // Notifier le parent
        if (onCancelled) {
          onCancelled();
        }
      } else {
        showError(
          result.message || `Impossible d'annuler ${analysisType}`,
          'Erreur d\'annulation'
        );
      }
    } catch (error) {
      showError(
        `Erreur lors de l'annulation de ${analysisType}`,
        'Erreur'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return variant === 'icon' ? 'w-6 h-6' : 'px-2 py-1 text-xs';
      case 'lg':
        return variant === 'icon' ? 'w-10 h-10' : 'px-6 py-3 text-base';
      default:
        return variant === 'icon' ? 'w-8 h-8' : 'px-4 py-2 text-sm';
    }
  };

  const getIconSize = () => {
    switch (size) {
      case 'sm': return 12;
      case 'lg': return 20;
      default: return 16;
    }
  };

  if (showConfirm) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-gradient-to-br from-gray-800 to-gray-900 border border-gray-700 rounded-xl p-6 max-w-md mx-4 shadow-2xl">
          <div className="flex items-center space-x-3 mb-4">
            <div className="w-10 h-10 bg-orange-600/20 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-orange-400" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white">
                Confirmer l'annulation
              </h3>
              <p className="text-gray-400 text-sm">
                Cette action ne peut pas être annulée
              </p>
            </div>
          </div>
          
          <p className="text-gray-300 mb-6">
            Êtes-vous sûr de vouloir annuler cette {analysisType} ? 
            Le processus en cours sera interrompu et les résultats partiels seront perdus.
          </p>
          
          <div className="flex space-x-3">
            <button
              onClick={() => setShowConfirm(false)}
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-lg transition-colors disabled:opacity-50"
            >
              Annuler
            </button>
            <button
              onClick={handleCancel}
              disabled={isLoading}
              className="flex-1 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center space-x-2"
            >
              {isLoading ? (
                <>
                  <Loader className="w-4 h-4 animate-spin" />
                  <span>Annulation...</span>
                </>
              ) : (
                <>
                  <X className="w-4 h-4" />
                  <span>Confirmer</span>
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'icon') {
    return (
      <button
        onClick={() => setShowConfirm(true)}
        disabled={disabled || isLoading}
        className={`
          ${getSizeClasses()}
          bg-red-600/20 hover:bg-red-600/30 text-red-400 hover:text-red-300
          rounded-lg border border-red-600/30 hover:border-red-600/50
          transition-all duration-200 flex items-center justify-center
          disabled:opacity-50 disabled:cursor-not-allowed
          hover:scale-105 active:scale-95
        `}
        title={`Annuler ${analysisType}`}
      >
        {isLoading ? (
          <Loader className={`w-${getIconSize()/4} h-${getIconSize()/4} animate-spin`} />
        ) : (
          <X size={getIconSize()} />
        )}
      </button>
    );
  }

  return (
    <button
      onClick={() => setShowConfirm(true)}
      disabled={disabled || isLoading}
      className={`
        ${getSizeClasses()}
        bg-red-600 hover:bg-red-700 text-white
        rounded-lg transition-all duration-200 flex items-center justify-center space-x-2
        disabled:opacity-50 disabled:cursor-not-allowed
        hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl
      `}
    >
      {isLoading ? (
        <>
          <Loader size={getIconSize()} className="animate-spin" />
          <span>Annulation...</span>
        </>
      ) : (
        <>
          <X size={getIconSize()} />
          <span>Annuler</span>
        </>
      )}
    </button>
  );
};

export default CancelAnalysisButton;
