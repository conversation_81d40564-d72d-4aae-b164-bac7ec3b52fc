import { useState, useEffect } from 'react';
import {
  X,
  User,
  Mail,
  Calendar,
  Shield,
  Save,
  Eye,
  EyeOff,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Edit,
} from 'lucide-react';
import { userService, UpdateUserData } from '../../services/userService';
import { useModalContext } from '../../contexts/ModalContext';
import SessionHistory from './SessionHistory';

interface UserDetailModalProps {
  user: any;
  isOpen: boolean;
  onClose: () => void;
  onUserUpdated: () => void;
}

export default function UserDetailModal({
  user,
  isOpen,
  onClose,
  onUserUpdated,
}: UserDetailModalProps) {
  const { showAlert } = useModalContext();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [activeTab, setActiveTab] = useState<'details' | 'sessions'>('details');
  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    username: '',
    email: '',
    date_of_birth: '',
    gender: '',
    role: 'user' as 'user' | 'admin',
    password: '',
  });

  useEffect(() => {
    if (user) {
      setFormData({
        first_name: user.first_name || '',
        last_name: user.last_name || '',
        username: user.username || '',
        email: user.email || '',
        date_of_birth: user.date_of_birth || '',
        gender: user.gender || '',
        role: user.role || 'user',
        password: '',
      });
    }
  }, [user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSave = async () => {
    try {
      setLoading(true);

      // Prepare update data (only include changed fields)
      const updateData: UpdateUserData = {};

      if (formData.first_name !== user.first_name) updateData.first_name = formData.first_name;
      if (formData.last_name !== user.last_name) updateData.last_name = formData.last_name;
      if (formData.username !== user.username) updateData.username = formData.username;
      if (formData.email !== user.email) updateData.email = formData.email;
      if (formData.date_of_birth !== user.date_of_birth)
        updateData.date_of_birth = formData.date_of_birth;
      if (formData.gender !== user.gender) updateData.gender = formData.gender;
      if (formData.role !== user.role) updateData.role = formData.role;
      if (formData.password.trim()) updateData.password = formData.password;

      if (Object.keys(updateData).length === 0) {
        showAlert('No changes to save', 'info');
        setIsEditing(false);
        return;
      }

      await userService.update(user._id, updateData);
      showAlert('User updated successfully', 'success');
      setIsEditing(false);
      onUserUpdated();
    } catch (error: any) {
      showAlert(error.response?.data?.msg || 'Failed to update user', 'error');
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return 'Not provided';
    return new Date(dateString).toLocaleDateString();
  };

  const getStatusColor = (user: any) => {
    if (user.banned) return 'text-red-400';
    if (!user.email_verified) return 'text-yellow-400';
    if (user.active) return 'text-green-400';
    return 'text-gray-400';
  };

  const getStatusText = (user: any) => {
    if (user.banned) return 'Banned';
    if (!user.email_verified) return 'Unverified';
    if (user.active) return 'Active';
    return 'Inactive';
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-800 border border-gray-700 rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-600 rounded-full flex items-center justify-center">
              <User size={20} className="text-gray-300" />
            </div>
            <div>
              <h2 className="text-xl font-bold text-white">
                {user.first_name} {user.last_name}
              </h2>
              <p className="text-gray-400 text-sm">@{user.username}</p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {!isEditing && (
              <button
                onClick={() => setIsEditing(true)}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
              >
                <Edit size={16} />
                <span>Edit</span>
              </button>
            )}

            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white p-2 rounded-lg hover:bg-gray-700 transition-colors"
            >
              <X size={20} />
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-700">
          <div className="flex space-x-0">
            <button
              onClick={() => setActiveTab('details')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'details'
                  ? 'border-purple-500 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-white'
              }`}
            >
              User Details
            </button>
            <button
              onClick={() => setActiveTab('sessions')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'sessions'
                  ? 'border-purple-500 text-purple-400'
                  : 'border-transparent text-gray-400 hover:text-white'
              }`}
            >
              Sessions & Login History
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {activeTab === 'details' ? (
            <div className="space-y-6">
              {/* Status Section */}
              <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                <h3 className="text-white font-medium mb-3 flex items-center">
                  <CheckCircle size={16} className="mr-2" />
                  Account Status
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-gray-400 text-sm">Status</p>
                    <div className={`flex items-center ${getStatusColor(user)}`}>
                      {user.banned ? (
                        <XCircle size={16} className="mr-1" />
                      ) : user.email_verified ? (
                        <CheckCircle size={16} className="mr-1" />
                      ) : (
                        <XCircle size={16} className="mr-1" />
                      )}
                      <span className="font-medium">{getStatusText(user)}</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">Role</p>
                    <span
                      className={`inline-block px-2 py-1 rounded text-xs ${
                        user.role === 'admin'
                          ? 'bg-purple-600 text-white'
                          : 'bg-blue-600 text-white'
                      }`}
                    >
                      {user.role}
                    </span>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">Email Verified</p>
                    <span className={user.email_verified ? 'text-green-400' : 'text-red-400'}>
                      {user.email_verified ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div>
                    <p className="text-gray-400 text-sm">Created</p>
                    <span className="text-white">
                      {user.created_at
                        ? formatDate(user.created_at)
                        : user._id
                          ? formatDate(
                              new Date(parseInt(user._id.substring(0, 8), 16) * 1000).toISOString()
                            )
                          : 'Not available'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Personal Information */}
              <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                <h3 className="text-white font-medium mb-4 flex items-center">
                  <User size={16} className="mr-2" />
                  Personal Information
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-gray-400 text-sm mb-1">First Name</label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="first_name"
                        value={formData.first_name}
                        onChange={handleInputChange}
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    ) : (
                      <p className="text-white">{user.first_name || 'Not provided'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-gray-400 text-sm mb-1">Last Name</label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="last_name"
                        value={formData.last_name}
                        onChange={handleInputChange}
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    ) : (
                      <p className="text-white">{user.last_name || 'Not provided'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-gray-400 text-sm mb-1">Username</label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="username"
                        value={formData.username}
                        onChange={handleInputChange}
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    ) : (
                      <p className="text-white">@{user.username}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-gray-400 text-sm mb-1">Email</label>
                    {isEditing ? (
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    ) : (
                      <p className="text-white">{user.email}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-gray-400 text-sm mb-1">Date of Birth</label>
                    {isEditing ? (
                      <input
                        type="text"
                        name="date_of_birth"
                        value={formData.date_of_birth}
                        onChange={handleInputChange}
                        placeholder="DD/MM/YYYY"
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      />
                    ) : (
                      <p className="text-white">{user.date_of_birth || 'Not provided'}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-gray-400 text-sm mb-1">Gender</label>
                    {isEditing ? (
                      <select
                        name="gender"
                        value={formData.gender}
                        onChange={handleInputChange}
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <option value="">Select Gender</option>
                        <option value="Male">Male</option>
                        <option value="Female">Female</option>
                        <option value="Other">Other</option>
                      </select>
                    ) : (
                      <p className="text-white">{user.gender || 'Not provided'}</p>
                    )}
                  </div>
                </div>
              </div>

              {/* Admin Settings */}
              {isEditing && (
                <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                  <h3 className="text-white font-medium mb-4 flex items-center">
                    <Shield size={16} className="mr-2" />
                    Admin Settings
                  </h3>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-gray-400 text-sm mb-1">Role</label>
                      <select
                        name="role"
                        value={formData.role}
                        onChange={handleInputChange}
                        className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                      >
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-gray-400 text-sm mb-1">
                        New Password (optional)
                      </label>
                      <div className="relative">
                        <input
                          type={showPassword ? 'text' : 'password'}
                          name="password"
                          value={formData.password}
                          onChange={handleInputChange}
                          placeholder="Leave empty to keep current password"
                          className="w-full bg-gray-700/50 border border-gray-600 rounded-lg px-3 py-2 pr-10 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                        >
                          {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                        </button>
                      </div>
                      <p className="text-gray-500 text-xs mt-1">
                        Password must contain at least 8 characters, one uppercase, one lowercase,
                        one number and one symbol
                      </p>
                    </div>
                  </div>
                </div>
              )}

              {/* Additional Info */}
              <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                <h3 className="text-white font-medium mb-3 flex items-center">
                  <AlertTriangle size={16} className="mr-2" />
                  Additional Information
                </h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-400">User ID</p>
                    <p className="text-white font-mono">{user._id}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">2FA Enabled</p>
                    <span className={user['2fa_enabled'] ? 'text-green-400' : 'text-red-400'}>
                      {user['2fa_enabled'] ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div>
                    <p className="text-gray-400">Last Login</p>
                    <p className="text-white">
                      {user.last_login ? formatDate(user.last_login) : 'Never'}
                    </p>
                  </div>
                  <div>
                    <p className="text-gray-400">Login Count</p>
                    <p className="text-white">{user.login_count || 0}</p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <SessionHistory userId={user._id} />
          )}
        </div>

        {/* Footer */}
        {isEditing && (
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-700">
            <button
              onClick={() => {
                setIsEditing(false);
                // Reset form data
                setFormData({
                  first_name: user.first_name || '',
                  last_name: user.last_name || '',
                  username: user.username || '',
                  email: user.email || '',
                  date_of_birth: user.date_of_birth || '',
                  gender: user.gender || '',
                  role: user.role || 'user',
                  password: '',
                });
              }}
              className="px-4 py-2 text-gray-400 hover:text-white border border-gray-600 rounded-lg hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={loading}
              className="bg-purple-600 hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save size={16} />
              )}
              <span>{loading ? 'Saving...' : 'Save Changes'}</span>
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
