import { useState, useEffect } from 'react';
import {
  User,
  Mail,
  Shield,
  CheckCircle,
  XCircle,
  Calendar,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Activity,
  Ban,
  UserCheck,
  Eye,
  AlertTriangle,
} from 'lucide-react';
import { userService } from '../services/userService';
import { useAuth } from '../hooks/useAuth';
import { useModalContext } from '../contexts/ModalContext';
import UserDetailModal from '../components/admin/UserDetailModal';
import CreateUserModal from '../components/admin/CreateUserModal';
import BanUserModal from '../components/modals/BanUserModal';
import UserActivityModal from '../components/admin/UserActivityModal';
import BanSuccessModal from '../components/modals/BanSuccessModal';
import UnbanUserModal from '../components/modals/UnbanUserModal';
import UnbanSuccessModal from '../components/modals/UnbanSuccessModal';
import DeleteUserModal from '../components/modals/DeleteUserModal';
import DeleteSuccessModal from '../components/modals/DeleteSuccessModal';

interface User {
  _id: string;
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  active: boolean;
  banned: boolean;
  email_verified: boolean;
  created_at: string;
  date_of_birth?: string;
  gender?: string;
}

export default function UserManagement() {
  const { user: currentUser } = useAuth();
  const { showAlert, showConfirm } = useModalContext();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'admin' | 'user'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'banned' | 'unverified'>(
    'all'
  );
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserDetail, setShowUserDetail] = useState(false);
  const [showCreateUser, setShowCreateUser] = useState(false);
  const [showBanModal, setShowBanModal] = useState(false);
  const [showBanSuccess, setShowBanSuccess] = useState(false);
  const [showUnbanModal, setShowUnbanModal] = useState(false);
  const [showUnbanSuccess, setShowUnbanSuccess] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [userToBan, setUserToBan] = useState<{ id: string; username: string } | null>(null);
  const [userToUnban, setUserToUnban] = useState<{ id: string; username: string } | null>(null);
  const [userToDelete, setUserToDelete] = useState<{ id: string; username: string } | null>(null);
  const [bannedUsername, setBannedUsername] = useState<string>('');
  const [unbannedUsername, setUnbannedUsername] = useState<string>('');
  const [deletedUsername, setDeletedUsername] = useState<string>('');
  const [banLoading, setBanLoading] = useState(false);
  const [unbanLoading, setUnbanLoading] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState(false);
  const [showActivityModal, setShowActivityModal] = useState(false);
  const [selectedUserForActivity, setSelectedUserForActivity] = useState<{
    id: string;
    username: string;
  } | null>(null);

  useEffect(() => {
    fetchUsers();
  }, []);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const response = await userService.getAll();
      setUsers(response.data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleBanUser = (userId: string, username: string) => {
    setUserToBan({ id: userId, username });
    setShowBanModal(true);
  };

  const confirmBanUser = async () => {
    if (!userToBan) return;

    try {
      setBanLoading(true);
      await userService.ban(userToBan.id);

      // Store the banned username and show success modal
      setBannedUsername(userToBan.username);
      setShowBanModal(false);
      setUserToBan(null);
      setShowBanSuccess(true);

      // Refresh the users list
      fetchUsers();
    } catch (error) {
      showAlert({
        message: '❌ Échec du bannissement. Veuillez réessayer.',
        type: 'error',
        title: 'Error Banning User',
      });
    } finally {
      setBanLoading(false);
    }
  };

  const closeBanModal = () => {
    if (!banLoading) {
      setShowBanModal(false);
      setUserToBan(null);
    }
  };

  const handleUnbanUser = (userId: string, username: string) => {
    setUserToUnban({ id: userId, username });
    setShowUnbanModal(true);
  };

  const confirmUnbanUser = async () => {
    if (!userToUnban) return;

    try {
      setUnbanLoading(true);
      await userService.unban(userToUnban.id);

      // Store the unbanned username and show success modal
      setUnbannedUsername(userToUnban.username);
      setShowUnbanModal(false);
      setUserToUnban(null);
      setShowUnbanSuccess(true);

      // Refresh the users list
      fetchUsers();
    } catch (error) {
      showAlert({
        message: '❌ Échec du débannissement. Veuillez réessayer.',
        type: 'error',
        title: 'Error Unbanning User',
      });
    } finally {
      setUnbanLoading(false);
    }
  };

  const closeUnbanModal = () => {
    if (!unbanLoading) {
      setShowUnbanModal(false);
      setUserToUnban(null);
    }
  };

  const handleDeleteUser = (userId: string, username: string) => {
    setUserToDelete({ id: userId, username });
    setShowDeleteModal(true);
  };

  const confirmDeleteUser = async () => {
    if (!userToDelete) return;

    try {
      setDeleteLoading(true);
      await userService.delete(userToDelete.id);

      // Store the deleted username and show success modal
      setDeletedUsername(userToDelete.username);
      setShowDeleteModal(false);
      setUserToDelete(null);
      setShowDeleteSuccess(true);

      // Refresh the users list
      fetchUsers();
    } catch (error) {
      showAlert({
        message: '❌ Échec de la suppression. Veuillez réessayer.',
        type: 'error',
        title: 'Error Deleting User',
      });
    } finally {
      setDeleteLoading(false);
    }
  };

  const closeDeleteModal = () => {
    if (!deleteLoading) {
      setShowDeleteModal(false);
      setUserToDelete(null);
    }
  };

  const handleViewUser = async (user: User) => {
    try {
      const response = await userService.getById(user._id);
      setSelectedUser(response.data);
      setShowUserDetail(true);
    } catch (error) {
      showAlert({
        message: 'Failed to load user details',
        type: 'error',
        title: 'Error Loading User',
      });
    }
  };

  const handleEditUser = async (user: User) => {
    try {
      const response = await userService.getById(user._id);
      setSelectedUser(response.data);
      setShowUserDetail(true);
    } catch (error) {
      showAlert({
        message: 'Failed to load user details',
        type: 'error',
        title: 'Error Loading User',
      });
    }
  };

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.first_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.last_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = roleFilter === 'all' || user.role === roleFilter;

    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && user.active && !user.banned && user.email_verified) ||
      (statusFilter === 'banned' && user.banned) ||
      (statusFilter === 'unverified' && !user.email_verified);

    return matchesSearch && matchesRole && matchesStatus;
  });

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-purple-600 text-white';
      case 'user':
        return 'bg-blue-600 text-white';
      default:
        return 'bg-gray-600 text-white';
    }
  };

  const getStatusColor = (user: User) => {
    if (user.banned) return 'text-red-400';
    if (!user.email_verified) return 'text-yellow-400';
    if (user.active) return 'text-green-400';
    return 'text-gray-400';
  };

  const getStatusText = (user: User) => {
    if (user.banned) return 'Banned';
    if (!user.email_verified) return 'Unverified';
    if (user.active) return 'Active';
    return 'Inactive';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getUserCreationDate = (user: User) => {
    // Try created_at field first
    if (user.created_at) {
      return formatDate(user.created_at);
    }
    // Fallback to extracting date from MongoDB ObjectId
    if (user._id) {
      try {
        const timestamp = parseInt(user._id.substring(0, 8), 16) * 1000;
        return formatDate(new Date(timestamp).toISOString());
      } catch (error) {
        return 'Unknown';
      }
    }
    return 'Unknown';
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-96">
        <div className="relative">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-600 border-t-purple-400"></div>
          <div className="absolute inset-0 rounded-full border-4 border-transparent border-r-purple-400 animate-pulse"></div>
        </div>
        <p className="text-gray-400 mt-6 text-lg">Loading users...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gradient-to-br from-red-900/40 to-red-800/20 backdrop-blur-xl border border-red-500/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
            <h3 className="text-xl font-bold text-red-400 mb-2">Error Loading Users</h3>
            <p className="text-red-300">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  const handleViewActivity = (userId: string, username: string) => {
    setSelectedUserForActivity({ id: userId, username });
    setShowActivityModal(true);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
              <User className="w-10 h-10 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white">User Management</h1>
              <p className="text-purple-200 mt-1">Manage all users in the system</p>
            </div>
          </div>
          <button
            onClick={() => setShowCreateUser(true)}
            className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-6 py-3 rounded-xl flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <Plus size={20} />
            <span>Create User</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl hover:shadow-blue-500/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Total Users</p>
              <p className="text-3xl font-bold text-white">{users.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-600/20 rounded-xl flex items-center justify-center">
              <User className="h-7 w-7 text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 hover:border-green-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl hover:shadow-green-500/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Active Users</p>
              <p className="text-3xl font-bold text-green-400">
                {users.filter((u) => u.active && !u.banned && u.email_verified).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-600/20 rounded-xl flex items-center justify-center">
              <CheckCircle className="h-7 w-7 text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 hover:border-red-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl hover:shadow-red-500/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Banned Users</p>
              <p className="text-3xl font-bold text-red-400">
                {users.filter((u) => u.banned).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-red-600/20 rounded-xl flex items-center justify-center">
              <Ban className="h-7 w-7 text-red-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl hover:shadow-purple-500/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Admins</p>
              <p className="text-3xl font-bold text-purple-400">
                {users.filter((u) => u.role === 'admin').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-600/20 rounded-xl flex items-center justify-center">
              <Shield className="h-7 w-7 text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-xl">
        <h2 className="text-xl font-bold text-white mb-6">Search & Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Search */}
          <div className="md:col-span-1">
            <label className="block text-sm font-medium text-gray-300 mb-2">Search Users</label>
            <div className="relative">
              <Search
                size={18}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name, username, or email..."
                className="w-full pl-11 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
              />
            </div>
          </div>

          {/* Role Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Filter by Role</label>
            <div className="relative">
              <Filter
                size={18}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value as any)}
                className="w-full pl-11 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors appearance-none"
              >
                <option value="all">All Roles</option>
                <option value="admin">Admin</option>
                <option value="user">User</option>
              </select>
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Filter by Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors appearance-none"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="banned">Banned</option>
              <option value="unverified">Unverified</option>
            </select>
          </div>
        </div>
      </div>

      {/* User List */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-xl">
        <div className="p-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Users ({filteredUsers.length})</h2>
            <div className="text-sm text-gray-400">
              Showing {filteredUsers.length} of {users.length} users
            </div>
          </div>

          {filteredUsers.length === 0 ? (
            <div className="text-center py-16">
              <div className="w-20 h-20 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-6">
                <User size={32} className="text-gray-500" />
              </div>
              <h3 className="text-lg font-medium text-gray-300 mb-2">No Users Found</h3>
              <p className="text-gray-500 text-center max-w-md mx-auto">
                {searchTerm || roleFilter !== 'all' || statusFilter !== 'all'
                  ? 'No users match your current filters. Try adjusting your search criteria.'
                  : 'No users have been created yet. Create your first user to get started.'}
              </p>
            </div>
          ) : (
            <div className="space-y-6">
              {filteredUsers.map((user, index) => (
                <div
                  key={user._id}
                  className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-300 transform hover:scale-[1.01] shadow-lg hover:shadow-xl hover:shadow-purple-500/10"
                  style={{ animationDelay: `${index * 50}ms` }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-6">
                      <div className="relative">
                        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
                          <User size={28} className="text-white" />
                        </div>
                        {user.role === 'admin' && (
                          <div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center border-2 border-gray-800">
                            <Shield size={12} className="text-white" />
                          </div>
                        )}
                      </div>

                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-xl font-bold text-white">
                            {user.first_name} {user.last_name}
                          </h3>
                          <span
                            className={`px-3 py-1 rounded-lg text-xs font-medium ${getRoleColor(user.role)}`}
                          >
                            {user.role.toUpperCase()}
                          </span>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                          <div className="flex items-center space-x-2 px-3 py-2 bg-gray-700/30 border border-gray-600/30 rounded-lg">
                            <User size={14} className="text-gray-400" />
                            <span className="text-gray-300 font-medium">@{user.username}</span>
                          </div>
                          <div className="flex items-center space-x-2 px-3 py-2 bg-gray-700/30 border border-gray-600/30 rounded-lg">
                            <Mail size={14} className="text-gray-400" />
                            <span className="text-gray-300 truncate">{user.email}</span>
                          </div>
                          <div className="flex items-center space-x-2 px-3 py-2 bg-gray-700/30 border border-gray-600/30 rounded-lg">
                            <Calendar size={14} className="text-gray-400" />
                            <span className="text-gray-300">{getUserCreationDate(user)}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col items-end space-y-4">
                      {/* Status */}
                      <div
                        className={`flex items-center space-x-2 px-3 py-2 rounded-lg border ${
                          user.banned
                            ? 'bg-red-500/20 border-red-500/30 text-red-400'
                            : user.email_verified
                              ? 'bg-green-500/20 border-green-500/30 text-green-400'
                              : 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400'
                        }`}
                      >
                        {user.banned ? (
                          <XCircle size={16} />
                        ) : user.email_verified ? (
                          <CheckCircle size={16} />
                        ) : (
                          <AlertTriangle size={16} />
                        )}
                        <span className="text-sm font-medium">{getStatusText(user)}</span>
                      </div>

                      {/* Actions */}
                      <div className="flex flex-wrap items-center gap-2">
                        <button
                          onClick={() => handleViewUser(user)}
                          className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg"
                        >
                          <Eye size={14} />
                          <span>View</span>
                        </button>

                        <button
                          onClick={() => handleEditUser(user)}
                          className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg"
                        >
                          <Edit size={14} />
                          <span>Edit</span>
                        </button>

                        <button
                          onClick={() => handleViewActivity(user._id, user.username)}
                          className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg"
                        >
                          <Activity size={14} />
                          <span>Activity</span>
                        </button>

                        {user.banned ? (
                          <button
                            onClick={() => handleUnbanUser(user._id, user.username)}
                            className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg"
                          >
                            <UserCheck size={14} />
                            <span>Unban</span>
                          </button>
                        ) : (
                          <button
                            onClick={() => handleBanUser(user._id, user.username)}
                            className="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                            disabled={user._id === currentUser?.id}
                          >
                            <Ban size={14} />
                            <span>Ban</span>
                          </button>
                        )}

                        <button
                          onClick={() => handleDeleteUser(user._id, user.username)}
                          className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                          disabled={user._id === currentUser?.id}
                        >
                          <Trash2 size={14} />
                          <span>Delete</span>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      {showUserDetail && selectedUser && (
        <UserDetailModal
          user={selectedUser}
          isOpen={showUserDetail}
          onClose={() => {
            setShowUserDetail(false);
            setSelectedUser(null);
          }}
          onUserUpdated={fetchUsers}
        />
      )}

      {showCreateUser && (
        <CreateUserModal
          isOpen={showCreateUser}
          onClose={() => setShowCreateUser(false)}
          onUserCreated={fetchUsers}
        />
      )}

      {showBanModal && userToBan && (
        <BanUserModal
          isOpen={showBanModal}
          onClose={closeBanModal}
          onConfirm={confirmBanUser}
          username={userToBan.username}
          loading={banLoading}
        />
      )}

      {showBanSuccess && (
        <BanSuccessModal
          isOpen={showBanSuccess}
          onClose={() => setShowBanSuccess(false)}
          username={bannedUsername}
        />
      )}

      {showUnbanModal && userToUnban && (
        <UnbanUserModal
          isOpen={showUnbanModal}
          onClose={closeUnbanModal}
          onConfirm={confirmUnbanUser}
          username={userToUnban.username}
          loading={unbanLoading}
        />
      )}

      {showUnbanSuccess && (
        <UnbanSuccessModal
          isOpen={showUnbanSuccess}
          onClose={() => setShowUnbanSuccess(false)}
          username={unbannedUsername}
        />
      )}

      {showDeleteModal && userToDelete && (
        <DeleteUserModal
          isOpen={showDeleteModal}
          onClose={closeDeleteModal}
          onConfirm={confirmDeleteUser}
          username={userToDelete.username}
          loading={deleteLoading}
        />
      )}

      {showDeleteSuccess && (
        <DeleteSuccessModal
          isOpen={showDeleteSuccess}
          onClose={() => setShowDeleteSuccess(false)}
          username={deletedUsername}
        />
      )}

      {/* User Activity Modal */}
      {showActivityModal && selectedUserForActivity && (
        <UserActivityModal
          isOpen={showActivityModal}
          onClose={() => setShowActivityModal(false)}
          userId={selectedUserForActivity.id}
          userName={selectedUserForActivity.username}
        />
      )}
    </div>
  );
}
