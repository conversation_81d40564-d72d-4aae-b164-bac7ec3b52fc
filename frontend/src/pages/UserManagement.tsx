import { useState, useEffect, useReducer, useCallback } from 'react';
import {
  User,
  Mail,
  Shield,
  CheckCircle,
  XCircle,
  Calendar,
  Search,
  Filter,
  Plus,
  Edit,
  Trash2,
  Activity,
  Ban,
  UserCheck,
  Eye,
  AlertTriangle,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import { userService } from '../services/userService';
import { useAuth } from '../hooks/useAuth';
import { useNotifications } from '../contexts/NotificationContext';
import UserDetailModal from '../components/admin/UserDetailModal';
import CreateUserModal from '../components/admin/CreateUserModal';
import BanUserModal from '../components/modals/BanUserModal';
import UserActivityModal from '../components/admin/UserActivityModal';
import UnbanUserModal from '../components/modals/UnbanUserModal';
import DeleteUserModal from '../components/modals/DeleteUserModal';

interface User {
  _id: string;
  first_name: string;
  last_name: string;
  username: string;
  email: string;
  role: 'admin' | 'user';
  active: boolean;
  banned: boolean;
  email_verified: boolean;
  created_at: string;
  date_of_birth?: string;
  gender?: string;
  _optimisticOperation?: number; // For tracking optimistic updates
}

interface Pagination {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface ModalState {
  type: 'ban' | 'unban' | 'delete' | null;
  isOpen: boolean;
  targetUser: { id: string; username: string } | null;
  loading: boolean;
}

type ModalAction =
  | { type: 'OPEN_MODAL'; modalType: ModalState['type']; user: { id: string; username: string } }
  | { type: 'CLOSE_MODAL' }
  | { type: 'SET_LOADING'; loading: boolean };

const modalReducer = (state: ModalState, action: ModalAction): ModalState => {
  switch (action.type) {
    case 'OPEN_MODAL':
      return {
        ...state,
        type: action.modalType,
        isOpen: true,
        targetUser: action.user,
        loading: false
      };
    case 'CLOSE_MODAL':
      return {
        ...state,
        isOpen: false,
        type: null,
        targetUser: null,
        loading: false
      };
    case 'SET_LOADING':
      return { ...state, loading: action.loading };
    default:
      return state;
  }
};

export default function UserManagement() {
  const { user: currentUser } = useAuth();
  const { showError, showSuccess } = useNotifications();

  // Core state
  const [users, setUsers] = useState<User[]>([]);
  const [allUsers, setAllUsers] = useState<User[]>([]); // For stats calculation
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Search and filters with debouncing
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<'all' | 'admin' | 'user'>('all');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'banned' | 'unverified'>('all');

  // Pagination
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // Modal state management with reducer
  const [modalState, dispatchModal] = useReducer(modalReducer, {
    type: null,
    isOpen: false,
    targetUser: null,
    loading: false
  });

  // Other modals (keeping separate for now)
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [showUserDetail, setShowUserDetail] = useState(false);
  const [showCreateUser, setShowCreateUser] = useState(false);
  const [showActivityModal, setShowActivityModal] = useState(false);
  const [selectedUserForActivity, setSelectedUserForActivity] = useState<{
    id: string;
    username: string;
  } | null>(null);

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  // Initial load and pagination effects
  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    fetchUsers(pagination.page);
  }, [debouncedSearchTerm, roleFilter, statusFilter]);

  // Role-based access control
  const canManageUser = useCallback((targetUser: User) => {
    if (!currentUser) return false;
    if (currentUser.role !== 'admin') return false;
    if (targetUser._id === currentUser.id) return false;
    return true;
  }, [currentUser]);

  // Fetch users with retry mechanism
  const fetchUsers = async (page = 1, retryCount = 0) => {
    try {
      setLoading(true);

      // TODO: Implement server-side pagination when backend supports it
      // For now, we'll simulate pagination on the frontend
      // Future implementation would use:
      // const response = await userService.getAll({
      //   page,
      //   limit: pagination.limit,
      //   search: debouncedSearchTerm,
      //   role: roleFilter !== 'all' ? roleFilter : undefined,
      //   status: statusFilter !== 'all' ? statusFilter : undefined
      // });

      const response = await userService.getAll();
      const allUsersData = response.data;

      // Store all users for stats calculation
      setAllUsers(allUsersData);

      // Apply filters
      const filtered = allUsersData.filter((user: User) => {
        const matchesSearch =
          user.first_name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          user.last_name.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          user.username.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
          user.email.toLowerCase().includes(debouncedSearchTerm.toLowerCase());

        const matchesRole = roleFilter === 'all' || user.role === roleFilter;

        const matchesStatus =
          statusFilter === 'all' ||
          (statusFilter === 'active' && user.active && !user.banned && user.email_verified) ||
          (statusFilter === 'banned' && user.banned) ||
          (statusFilter === 'unverified' && !user.email_verified);

        return matchesSearch && matchesRole && matchesStatus;
      });

      // Simulate pagination
      const startIndex = (page - 1) * pagination.limit;
      const endIndex = startIndex + pagination.limit;
      const paginatedUsers = filtered.slice(startIndex, endIndex);

      setUsers(paginatedUsers);
      setPagination(prev => ({
        ...prev,
        page,
        total: filtered.length,
        totalPages: Math.ceil(filtered.length / prev.limit)
      }));
      setError(null);

      // Show success notification only on initial load (page 1 and retryCount 0)
      if (page === 1 && retryCount === 0 && filtered.length > 0) {
        showSuccess(
          `Successfully loaded ${filtered.length} user${filtered.length === 1 ? '' : 's'}.`,
          'Users Loaded',
          3000
        );
      } else if (page === 1 && retryCount === 0 && filtered.length === 0) {
        showError(
          'No users found. You may need to create some users first.',
          'No Users Found',
          5000
        );
      } else if (retryCount > 0) {
        showSuccess(
          `Connection restored. Successfully loaded ${filtered.length} user${filtered.length === 1 ? '' : 's'}.`,
          'Retry Successful',
          4000
        );
      }
    } catch (err) {
      if (retryCount < 3) {
        // Check if it's a recoverable error (network errors, 5xx server errors)
        const isRecoverable = err instanceof Error && (
          err.message.includes('network') ||
          err.message.includes('timeout') ||
          err.message.includes('fetch')
        );

        if (isRecoverable) {
          // Exponential backoff with jitter: base delay * 2^retryCount + random jitter
          const baseDelay = 1000;
          const exponentialDelay = baseDelay * Math.pow(2, retryCount);
          const jitter = Math.random() * 500; // 0-500ms jitter
          const delay = exponentialDelay + jitter;

          setTimeout(() => fetchUsers(page, retryCount + 1), delay);
          return;
        }
      }

      const errorMessage = err instanceof Error ? err.message : 'Failed to load users';
      setError(errorMessage);
      showError(
        `${errorMessage}. Please try again.`,
        'Error Loading Users'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleBanUser = (userId: string, username: string) => {
    dispatchModal({
      type: 'OPEN_MODAL',
      modalType: 'ban',
      user: { id: userId, username }
    });
  };

  const confirmBanUser = async () => {
    if (!modalState.targetUser) return;

    // Generate operation ID for tracking
    const operationId = Date.now();

    try {
      dispatchModal({ type: 'SET_LOADING', loading: true });

      // Optimistic update with operation tracking
      setUsers(prev => prev.map(user =>
        user._id === modalState.targetUser!.id
          ? { ...user, banned: true, _optimisticOperation: operationId }
          : user
      ));

      await userService.ban(modalState.targetUser.id);

      // Clear optimistic operation flag on success
      setUsers(prev => prev.map(user =>
        user._id === modalState.targetUser!.id
          ? { ...user, _optimisticOperation: undefined }
          : user
      ));

      // Show success notification
      showSuccess(
        `User "${modalState.targetUser.username}" has been banned successfully. Access to the platform has been blocked and active sessions have been terminated.`,
        'User Banned'
      );

      // Close the modal
      dispatchModal({ type: 'CLOSE_MODAL' });

      // Refresh the users list
      fetchUsers(pagination.page);
    } catch (error) {
      // Revert optimistic update on failure (only if it matches our operation)
      setUsers(prev => prev.map(user =>
        user._id === modalState.targetUser!.id && user._optimisticOperation === operationId
          ? { ...user, banned: false, _optimisticOperation: undefined }
          : user
      ));

      showError(
        'Failed to ban user. Please try again.',
        'Ban User Failed'
      );
      dispatchModal({ type: 'CLOSE_MODAL' });
    } finally {
      dispatchModal({ type: 'SET_LOADING', loading: false });
    }
  };

  const closeBanModal = () => {
    if (!modalState.loading) {
      dispatchModal({ type: 'CLOSE_MODAL' });
    }
  };

  const handleUnbanUser = (userId: string, username: string) => {
    dispatchModal({
      type: 'OPEN_MODAL',
      modalType: 'unban',
      user: { id: userId, username }
    });
  };

  const confirmUnbanUser = async () => {
    if (!modalState.targetUser) return;

    // Generate operation ID for tracking
    const operationId = Date.now();

    try {
      dispatchModal({ type: 'SET_LOADING', loading: true });

      // Optimistic update with operation tracking
      setUsers(prev => prev.map(user =>
        user._id === modalState.targetUser!.id
          ? { ...user, banned: false, _optimisticOperation: operationId }
          : user
      ));

      await userService.unban(modalState.targetUser.id);

      // Clear optimistic operation flag on success
      setUsers(prev => prev.map(user =>
        user._id === modalState.targetUser!.id
          ? { ...user, _optimisticOperation: undefined }
          : user
      ));

      // Show success notification
      showSuccess(
        `User "${modalState.targetUser.username}" has been unbanned successfully. They can now access the platform again.`,
        'User Unbanned'
      );

      // Close the modal
      dispatchModal({ type: 'CLOSE_MODAL' });

      // Refresh the users list
      fetchUsers(pagination.page);
    } catch (error) {
      // Revert optimistic update on failure (only if it matches our operation)
      setUsers(prev => prev.map(user =>
        user._id === modalState.targetUser!.id && user._optimisticOperation === operationId
          ? { ...user, banned: true, _optimisticOperation: undefined }
          : user
      ));

      showError(
        'Failed to unban user. Please try again.',
        'Unban User Failed'
      );
      dispatchModal({ type: 'CLOSE_MODAL' });
    } finally {
      dispatchModal({ type: 'SET_LOADING', loading: false });
    }
  };

  const closeUnbanModal = () => {
    if (!modalState.loading) {
      dispatchModal({ type: 'CLOSE_MODAL' });
    }
  };

  const handleDeleteUser = (userId: string, username: string) => {
    dispatchModal({
      type: 'OPEN_MODAL',
      modalType: 'delete',
      user: { id: userId, username }
    });
  };

  const confirmDeleteUser = async () => {
    if (!modalState.targetUser) return;

    // Store user reference and position before optimistic update
    const userToRemove = users.find(u => u._id === modalState.targetUser!.id);
    const userIndex = users.findIndex(u => u._id === modalState.targetUser!.id);
    const operationId = Date.now();

    try {
      dispatchModal({ type: 'SET_LOADING', loading: true });

      // Optimistic update - mark user as being deleted
      setUsers(prev => prev.map(user =>
        user._id === modalState.targetUser!.id
          ? { ...user, _optimisticOperation: operationId }
          : user
      ).filter(user => user._id !== modalState.targetUser!.id));

      await userService.delete(modalState.targetUser.id);

      // Show success notification
      showSuccess(
        `User "${modalState.targetUser.username}" has been deleted successfully. All associated data has been removed.`,
        'User Deleted'
      );

      // Close the modal
      dispatchModal({ type: 'CLOSE_MODAL' });

      // Refresh the users list
      fetchUsers(pagination.page);
    } catch (error) {
      // Revert optimistic update on failure - restore user at original position
      if (userToRemove) {
        setUsers(prev => {
          const newUsers = [...prev];
          // Insert at original position or at the end if position is invalid
          const insertIndex = Math.min(userIndex, newUsers.length);
          newUsers.splice(insertIndex, 0, { ...userToRemove, _optimisticOperation: undefined });
          return newUsers;
        });
      }

      showError(
        'Failed to delete user. Please try again.',
        'Delete User Failed'
      );
      dispatchModal({ type: 'CLOSE_MODAL' });
    } finally {
      dispatchModal({ type: 'SET_LOADING', loading: false });
    }
  };

  const closeDeleteModal = () => {
    if (!modalState.loading) {
      dispatchModal({ type: 'CLOSE_MODAL' });
    }
  };

  const handleViewUser = async (user: User) => {
    try {
      const response = await userService.getById(user._id);
      setSelectedUser(response.data);
      setShowUserDetail(true);
    } catch (error) {
      showError(
        'Failed to load user details. Please try again.',
        'Error Loading User'
      );
    }
  };

  const handleEditUser = async (user: User) => {
    try {
      const response = await userService.getById(user._id);
      setSelectedUser(response.data);
      setShowUserDetail(true);
    } catch (error) {
      showError(
        'Failed to load user details. Please try again.',
        'Error Loading User'
      );
    }
  };

  // Since filtering is now done in fetchUsers, we use users directly
  const filteredUsers = users;

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-purple-600 text-white';
      case 'user':
        return 'bg-blue-600 text-white';
      default:
        return 'bg-gray-600 text-white';
    }
  };



  const getStatusText = (user: User) => {
    if (user.banned) return 'Banned';
    if (!user.email_verified) return 'Unverified';
    if (user.active) return 'Active';
    return 'Inactive';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const getUserCreationDate = (user: User) => {
    // Try created_at field first
    if (user.created_at) {
      return formatDate(user.created_at);
    }
    // Fallback to extracting date from MongoDB ObjectId
    if (user._id) {
      try {
        const timestamp = parseInt(user._id.substring(0, 8), 16) * 1000;
        return formatDate(new Date(timestamp).toISOString());
      } catch (error) {
        return 'Unknown';
      }
    }
    return 'Unknown';
  };

  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center h-96">
        <div className="relative">
          <div className="animate-spin rounded-full h-16 w-16 border-4 border-gray-600 border-t-purple-400"></div>
          <div className="absolute inset-0 rounded-full border-4 border-transparent border-r-purple-400 animate-pulse"></div>
        </div>
        <p className="text-gray-400 mt-6 text-lg">Loading users...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-gradient-to-br from-red-900/40 to-red-800/20 backdrop-blur-xl border border-red-500/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center justify-center">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
            <h3 className="text-xl font-bold text-red-400 mb-2">Error Loading Users</h3>
            <p className="text-red-300">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  const handleViewActivity = (userId: string, username: string) => {
    setSelectedUserForActivity({ id: userId, username });
    setShowActivityModal(true);
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
              <User className="w-10 h-10 text-white" />
            </div>
            <div>
              <h1 className="text-4xl font-bold text-white">User Management</h1>
              <p className="text-purple-200 mt-1">Manage all users in the system</p>
            </div>
          </div>
          <button
            onClick={() => setShowCreateUser(true)}
            className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-6 py-3 rounded-xl flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            <Plus size={20} />
            <span>Create User</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 hover:border-blue-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl hover:shadow-blue-500/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Total Users</p>
              <p className="text-3xl font-bold text-white">{allUsers.length}</p>
            </div>
            <div className="w-12 h-12 bg-blue-600/20 rounded-xl flex items-center justify-center">
              <User className="h-7 w-7 text-blue-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 hover:border-green-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl hover:shadow-green-500/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Active Users</p>
              <p className="text-3xl font-bold text-green-400">
                {allUsers.filter((u) => u.active && !u.banned && u.email_verified).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-green-600/20 rounded-xl flex items-center justify-center">
              <CheckCircle className="h-7 w-7 text-green-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 hover:border-red-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl hover:shadow-red-500/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Banned Users</p>
              <p className="text-3xl font-bold text-red-400">
                {allUsers.filter((u) => u.banned).length}
              </p>
            </div>
            <div className="w-12 h-12 bg-red-600/20 rounded-xl flex items-center justify-center">
              <Ban className="h-7 w-7 text-red-400" />
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-300 transform hover:scale-[1.02] shadow-lg hover:shadow-xl hover:shadow-purple-500/10">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm font-medium">Admins</p>
              <p className="text-3xl font-bold text-purple-400">
                {allUsers.filter((u) => u.role === 'admin').length}
              </p>
            </div>
            <div className="w-12 h-12 bg-purple-600/20 rounded-xl flex items-center justify-center">
              <Shield className="h-7 w-7 text-purple-400" />
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-xl">
        <h2 className="text-xl font-bold text-white mb-6">Search & Filters</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Search */}
          <div className="md:col-span-1">
            <label className="block text-sm font-medium text-gray-300 mb-2">Search Users</label>
            <div className="relative">
              <Search
                size={18}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name, username, or email..."
                className="w-full pl-11 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors"
              />
            </div>
          </div>

          {/* Role Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Filter by Role</label>
            <div className="relative">
              <Filter
                size={18}
                className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
              />
              <select
                value={roleFilter}
                onChange={(e) => setRoleFilter(e.target.value as any)}
                className="w-full pl-11 pr-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors appearance-none"
              >
                <option value="all">All Roles</option>
                <option value="admin">Admin</option>
                <option value="user">User</option>
              </select>
            </div>
          </div>

          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">Filter by Status</label>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value as any)}
              className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-colors appearance-none"
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="banned">Banned</option>
              <option value="unverified">Unverified</option>
            </select>
          </div>
        </div>
      </div>

      {/* User List */}
      <div className="bg-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl shadow-xl">
        <div className="p-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">Users ({pagination.total})</h2>
            <div className="text-sm text-gray-400">
              Showing {filteredUsers.length} of {pagination.total} users (Page {pagination.page} of {pagination.totalPages})
            </div>
          </div>

          {filteredUsers.length === 0 ? (
            <div className="text-center py-16">
              <div className="w-20 h-20 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-6">
                <User size={32} className="text-gray-500" />
              </div>
              <h3 className="text-lg font-medium text-gray-300 mb-2">No Users Found</h3>
              <p className="text-gray-500 text-center max-w-md mx-auto">
                {debouncedSearchTerm || roleFilter !== 'all' || statusFilter !== 'all'
                  ? 'No users match your current filters. Try adjusting your search criteria.'
                  : 'No users have been created yet. Create your first user to get started.'}
              </p>
            </div>
          ) : (
            <>
              <div className="space-y-6">
                {filteredUsers.map((user, index) => (
                  <div
                    key={user._id}
                    className={`bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-xl p-6 hover:border-purple-500/50 transition-all duration-300 transform hover:scale-[1.01] shadow-lg hover:shadow-xl hover:shadow-purple-500/10 ${
                      user._optimisticOperation ? 'opacity-60 pointer-events-none' : ''
                    }`}
                    style={{ animationDelay: `${index * 50}ms` }}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-6">
                        <div className="relative">
                          <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-cyan-500 rounded-2xl flex items-center justify-center shadow-lg">
                            <User size={28} className="text-white" />
                          </div>
                          {user.role === 'admin' && (
                            <div className="absolute -top-1 -right-1 w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center border-2 border-gray-800">
                              <Shield size={12} className="text-white" />
                            </div>
                          )}
                          {user._optimisticOperation && (
                            <div className="absolute inset-0 bg-gray-900/50 rounded-2xl flex items-center justify-center">
                              <div className="animate-spin rounded-full h-6 w-6 border-2 border-gray-300 border-t-purple-400"></div>
                            </div>
                          )}
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-xl font-bold text-white">
                              {user.first_name} {user.last_name}
                            </h3>
                            <span
                              className={`px-3 py-1 rounded-lg text-xs font-medium ${getRoleColor(user.role)}`}
                            >
                              {user.role.toUpperCase()}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="flex items-center space-x-2 px-3 py-2 bg-gray-700/30 border border-gray-600/30 rounded-lg">
                              <User size={14} className="text-gray-400" />
                              <span className="text-gray-300 font-medium">@{user.username}</span>
                            </div>
                            <div className="flex items-center space-x-2 px-3 py-2 bg-gray-700/30 border border-gray-600/30 rounded-lg">
                              <Mail size={14} className="text-gray-400" />
                              <span className="text-gray-300 truncate">{user.email}</span>
                            </div>
                            <div className="flex items-center space-x-2 px-3 py-2 bg-gray-700/30 border border-gray-600/30 rounded-lg">
                              <Calendar size={14} className="text-gray-400" />
                              <span className="text-gray-300">{getUserCreationDate(user)}</span>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex flex-col items-end space-y-4">
                        {/* Status */}
                        <div
                          className={`flex items-center space-x-2 px-3 py-2 rounded-lg border ${
                            user.banned
                              ? 'bg-red-500/20 border-red-500/30 text-red-400'
                              : user.email_verified
                                ? 'bg-green-500/20 border-green-500/30 text-green-400'
                                : 'bg-yellow-500/20 border-yellow-500/30 text-yellow-400'
                          }`}
                        >
                          {user.banned ? (
                            <XCircle size={16} />
                          ) : user.email_verified ? (
                            <CheckCircle size={16} />
                          ) : (
                            <AlertTriangle size={16} />
                          )}
                          <span className="text-sm font-medium">{getStatusText(user)}</span>
                        </div>

                        {/* Actions */}
                        <div className="flex flex-wrap items-center gap-2">
                          <button
                            onClick={() => handleViewUser(user)}
                            className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg"
                          >
                            <Eye size={14} />
                            <span>View</span>
                          </button>

                          <button
                            onClick={() => handleEditUser(user)}
                            className="bg-gradient-to-r from-yellow-600 to-yellow-700 hover:from-yellow-700 hover:to-yellow-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg"
                          >
                            <Edit size={14} />
                            <span>Edit</span>
                          </button>

                          <button
                            onClick={() => handleViewActivity(user._id, user.username)}
                            className="bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg"
                          >
                            <Activity size={14} />
                            <span>Activity</span>
                          </button>

                          {canManageUser(user) && (
                            <>
                              {user.banned ? (
                                <button
                                  onClick={() => handleUnbanUser(user._id, user.username)}
                                  className="bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg"
                                >
                                  <UserCheck size={14} />
                                  <span>Unban</span>
                                </button>
                              ) : (
                                <button
                                  onClick={() => handleBanUser(user._id, user.username)}
                                  className="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg"
                                >
                                  <Ban size={14} />
                                  <span>Ban</span>
                                </button>
                              )}

                              <button
                                onClick={() => handleDeleteUser(user._id, user.username)}
                                className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-all duration-200 transform hover:scale-105 text-sm shadow-lg"
                              >
                                <Trash2 size={14} />
                                <span>Delete</span>
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Pagination Controls */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-8 pt-6 border-t border-gray-700/50">
                  <div className="text-sm text-gray-400">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to {Math.min(pagination.page * pagination.limit, pagination.total)} of {pagination.total} users
                  </div>

                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => fetchUsers(pagination.page - 1)}
                      disabled={pagination.page === 1}
                      className="px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600/50 transition-colors flex items-center space-x-1"
                    >
                      <ChevronLeft size={16} />
                      <span>Previous</span>
                    </button>

                    <div className="flex items-center space-x-1">
                      {(() => {
                        const maxVisiblePages = 5;
                        const halfWindow = Math.floor(maxVisiblePages / 2);
                        let startPage = Math.max(1, pagination.page - halfWindow);
                        let endPage = Math.min(pagination.totalPages, startPage + maxVisiblePages - 1);

                        // Adjust start page if we're near the end
                        if (endPage - startPage + 1 < maxVisiblePages) {
                          startPage = Math.max(1, endPage - maxVisiblePages + 1);
                        }

                        const pages = [];

                        // Add first page and ellipsis if needed
                        if (startPage > 1) {
                          pages.push(
                            <button
                              key={1}
                              onClick={() => fetchUsers(1)}
                              className="px-3 py-2 rounded-lg text-sm font-medium transition-colors bg-gray-700/50 border border-gray-600 text-gray-300 hover:bg-gray-600/50"
                            >
                              1
                            </button>
                          );
                          if (startPage > 2) {
                            pages.push(
                              <span key="ellipsis-start" className="px-2 text-gray-400">...</span>
                            );
                          }
                        }

                        // Add visible page numbers
                        for (let i = startPage; i <= endPage; i++) {
                          pages.push(
                            <button
                              key={i}
                              onClick={() => fetchUsers(i)}
                              className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                                pagination.page === i
                                  ? 'bg-purple-600 text-white'
                                  : 'bg-gray-700/50 border border-gray-600 text-gray-300 hover:bg-gray-600/50'
                              }`}
                            >
                              {i}
                            </button>
                          );
                        }

                        // Add ellipsis and last page if needed
                        if (endPage < pagination.totalPages) {
                          if (endPage < pagination.totalPages - 1) {
                            pages.push(
                              <span key="ellipsis-end" className="px-2 text-gray-400">...</span>
                            );
                          }
                          pages.push(
                            <button
                              key={pagination.totalPages}
                              onClick={() => fetchUsers(pagination.totalPages)}
                              className="px-3 py-2 rounded-lg text-sm font-medium transition-colors bg-gray-700/50 border border-gray-600 text-gray-300 hover:bg-gray-600/50"
                            >
                              {pagination.totalPages}
                            </button>
                          );
                        }

                        return pages;
                      })()}
                    </div>

                    <button
                      onClick={() => fetchUsers(pagination.page + 1)}
                      disabled={pagination.page === pagination.totalPages}
                      className="px-3 py-2 bg-gray-700/50 border border-gray-600 rounded-lg text-white disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-600/50 transition-colors flex items-center space-x-1"
                    >
                      <span>Next</span>
                      <ChevronRight size={16} />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* Modals */}
      {showUserDetail && selectedUser && (
        <UserDetailModal
          user={selectedUser}
          isOpen={showUserDetail}
          onClose={() => {
            setShowUserDetail(false);
            setSelectedUser(null);
          }}
          onUserUpdated={() => fetchUsers(pagination.page)}
        />
      )}

      {showCreateUser && (
        <CreateUserModal
          isOpen={showCreateUser}
          onClose={() => setShowCreateUser(false)}
          onUserCreated={() => {
            fetchUsers(pagination.page);
            showSuccess('New user created successfully.', 'User Created');
          }}
        />
      )}

      {/* Ban Modal */}
      {modalState.type === 'ban' && modalState.isOpen && modalState.targetUser && (
        <BanUserModal
          isOpen={modalState.isOpen}
          onClose={closeBanModal}
          onConfirm={confirmBanUser}
          username={modalState.targetUser.username}
          loading={modalState.loading}
        />
      )}

      {/* Unban Modal */}
      {modalState.type === 'unban' && modalState.isOpen && modalState.targetUser && (
        <UnbanUserModal
          isOpen={modalState.isOpen}
          onClose={closeUnbanModal}
          onConfirm={confirmUnbanUser}
          username={modalState.targetUser.username}
          loading={modalState.loading}
        />
      )}

      {/* Delete Modal */}
      {modalState.type === 'delete' && modalState.isOpen && modalState.targetUser && (
        <DeleteUserModal
          isOpen={modalState.isOpen}
          onClose={closeDeleteModal}
          onConfirm={confirmDeleteUser}
          username={modalState.targetUser.username}
          loading={modalState.loading}
        />
      )}

      {/* User Activity Modal */}
      {showActivityModal && selectedUserForActivity && (
        <UserActivityModal
          isOpen={showActivityModal}
          onClose={() => setShowActivityModal(false)}
          userId={selectedUserForActivity.id}
          userName={selectedUserForActivity.username}
        />
      )}
    </div>
  );
}
