import React, { useState, useEffect, useCallback } from 'react';
import { useModalContext } from '../contexts/ModalContext';
import { useToast } from '../hooks/useToast';
import { useAsyncError } from '../hooks/useAsyncError';
import { ToastContainer } from '../components/Toast';
import { downloadMarkdownReport, downloadSingleVulnerabilityMarkdown } from '../utils/markdownExporter';

// Note: Orbital animations are now handled in ai-animations.css
import {
  BarChart3,
  Upload,
  Play,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  Download,
  RefreshCw,
  X,
  FileText,
  Activity,
  Zap,
  Shield,
  Trash2,
  RotateCcw,
  Filter,
  Search,
  Grid,
  List,
  Brain,
  Cpu,
  Network,
  Sparkles,
  Layers,
  Orbit
} from 'lucide-react';
import {
  analyticsService,
  analyticsUtils,
  AnalysisListItem,
  AnalysisStatus,
  VulnerabilityInfo,
  AnalysisReport
} from '../services/analyticsService';
import { activeConfig } from '../config/api';
import VulnerabilityCard from '../components/analytics/VulnerabilityCard';
import VulnerabilityStats from '../components/analytics/VulnerabilityStats';

import '../styles/ai-animations.css';

// TypeScript interfaces for better type safety
interface AnalyticsStatistics {
  total_analyses: number;
  completed_analyses: number;
  failed_analyses: number;
  pending_analyses: number;
  running_analyses: number;
  full_reports: number;
  basic_reports: number;
}

interface StatusResult {
  error?: boolean;
  message?: string;
  analysis_id?: string;
  status?: string;
  progress?: number;
  started_at?: string;
  completed_at?: string;
  elapsed_time?: string;
  processing_speed?: number;
  estimated_time_remaining?: string;
}

interface ToastNotification {
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  duration?: number;
}

interface ErrorState {
  hasError: boolean;
  message: string;
  details?: string;
}



const Analytics: React.FC = () => {
  // Modal context for custom alerts and confirmations
  const { showAlert, showConfirm } = useModalContext();

  // Custom hooks for error handling and toasts
  const { toasts, showToast, hideToast } = useToast();
  const { error: asyncError, isLoading: asyncLoading, executeAsync, clearError } = useAsyncError();

  // States for analyses
  const [analyses, setAnalyses] = useState<AnalysisListItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<AnalyticsStatistics | null>(null);

  // States for analysis form
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [scanIds, setScanIds] = useState('scan1,scan2,scan3');
  const [analysisType, setAnalysisType] = useState<'full' | 'basic'>('full');
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [statusCheckId, setStatusCheckId] = useState('');
  const [statusResult, setStatusResult] = useState<StatusResult | null>(null);
  const [checkingStatus, setCheckingStatus] = useState(false);

  // States for tracking running analyses
  const [runningAnalyses, setRunningAnalyses] = useState<Map<string, AnalysisStatus>>(new Map());
  const [selectedAnalysis, setSelectedAnalysis] = useState<AnalysisListItem | null>(null);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportData, setReportData] = useState<AnalysisReport | null>(null);

  // States for vulnerability display
  const [vulnerabilities, setVulnerabilities] = useState<VulnerabilityInfo[]>([]);
  const [filteredVulnerabilities, setFilteredVulnerabilities] = useState<VulnerabilityInfo[]>([]);
  const [selectedVulnerability, setSelectedVulnerability] = useState<VulnerabilityInfo | null>(null);
  const [showVulnerabilityModal, setShowVulnerabilityModal] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  // Enhanced error handler with better context
  const handleError = useCallback((error: unknown, context: string) => {
    console.error(`Error in ${context}:`, error);
    const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';

    showToast({
      type: 'error',
      message: `Failed to ${context}. ${errorMessage}`,
      duration: 6000,
      action: {
        label: 'Retry',
        onClick: () => {
          // Context-specific retry logic could be added here
          clearError();
        }
      }
    });
  }, [showToast, clearError]);

  // WebSocket connection for real-time analysis status updates
  useEffect(() => {
    // Note: WebSocket implementation would replace polling for better performance
    // const ws = new WebSocket(`${activeConfig.WS_BASE_URL}/analysis-status`);
    //
    // ws.onmessage = (event) => {
    //   const statusUpdate = JSON.parse(event.data);
    //   setRunningAnalyses(prev => {
    //     const updated = new Map(prev);
    //     if (statusUpdate.status === 'completed' || statusUpdate.status === 'failed') {
    //       updated.delete(statusUpdate.analysis_id);
    //       loadAnalyses(); // Refresh completed analyses
    //     } else {
    //       updated.set(statusUpdate.analysis_id, statusUpdate);
    //     }
    //     return updated;
    //   });
    // };
    //
    // return () => ws.close();

    // For now, continue with polling but with better error handling
    const pollInterval = setInterval(() => {
      if (runningAnalyses.size > 0) {
        // Poll only if there are running analyses
        Array.from(runningAnalyses.keys()).forEach(analysisId => {
          checkAnalysisStatusById(analysisId);
        });
      }
    }, 5000); // Reduced frequency to 5 seconds

    return () => clearInterval(pollInterval);
  }, [runningAnalyses.size]);

  // Load data on mount
  useEffect(() => {
    // Automatically load analysis history
    loadAnalyses();
    loadStatistics();


  }, []);

  // Polling for running analyses
  useEffect(() => {
    const interval = setInterval(() => {
      updateRunningAnalyses();
    }, 3000); // Check every 3 seconds

    return () => clearInterval(interval);
  }, [runningAnalyses]);

  const loadAnalyses = async () => {
    setLoading(true);
    const response = await executeAsync(async () => {
      return await analyticsService.getAllAnalyses(1, 50);
    });

    if (response) {
      setAnalyses(response.analyses || []);
    } else {
      handleError(asyncError, 'load analyses');
    }
    setLoading(false);
  };

  const loadStatistics = async () => {
    const stats = await executeAsync(async () => {
      return await analyticsService.getAnalyticsStatistics();
    });

    if (stats) {
      setStatistics(stats);
    } else {
      handleError(asyncError, 'load statistics');
    }
  };

  // Helper function to check analysis status by ID (for WebSocket/polling)
  const checkAnalysisStatusById = async (analysisId: string) => {
    try {
      const status = await analyticsService.checkAnalysisStatus(analysisId);
      if (status.status === 'completed' || status.status === 'failed') {
        setRunningAnalyses(prev => {
          const updated = new Map(prev);
          updated.delete(analysisId);
          return updated;
        });
        loadAnalyses(); // Refresh the analyses list

        if (status.status === 'completed') {
          showToast({
            type: 'success',
            message: `Analysis ${analysisId.substring(0, 8)}... completed!`,
            duration: 5000
          });
        } else {
          showToast({
            type: 'error',
            message: `Analysis ${analysisId.substring(0, 8)}... failed!`,
            duration: 5000
          });
        }
      } else {
        setRunningAnalyses(prev => {
          const updated = new Map(prev);
          updated.set(analysisId, status);
          return updated;
        });
      }
    } catch (error) {
      // Silently handle errors for background polling
      console.warn(`Failed to check status for analysis ${analysisId}:`, error);
    }
  };

  const updateRunningAnalyses = async () => {
    const updates = new Map<string, AnalysisStatus>();

    for (const [analysisId, previousStatus] of runningAnalyses) {
      try {
        const status = await analyticsService.getAnalysisStatus(analysisId);

        // Check if the analysis just finished
        if ((status.status === 'completed' || status.status === 'failed') &&
            previousStatus.status !== 'completed' && previousStatus.status !== 'failed') {

          // Display completion notification with more details
          if (status.status === 'completed') {
            console.log(`✅ Analysis completed: ${status.filename || analysisId}`);
            if (status.elapsed_time) {
              console.log(`⏱️ Total duration: ${status.elapsed_time}`);
            }
            if (status.total_vulnerabilities !== undefined) {
              console.log(`🔍 ${status.total_vulnerabilities} vulnerabilities analyzed`);
            }
            // Optional: Add toast notification here
          } else {
            console.log(`❌ Analysis failed: ${status.filename || analysisId}`);
          }

          // Reload the analysis list
          loadAnalyses();

          // Don't add the completed analysis to the running analyses list
          // It will be automatically removed from the "running" view
        } else if (status.status !== 'completed' && status.status !== 'failed') {
          // Keep only analyses that are not finished (pending, processing, running)
          updates.set(analysisId, status);
        }
      } catch (error) {
        console.error(`Error checking status for ${analysisId}:`, error);
        // In case of error, keep the analysis in the list to retry
        updates.set(analysisId, previousStatus);
      }
    }

    setRunningAnalyses(updates);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const validation = analyticsUtils.validateCSVFile(file);
      if (validation.isValid) {
        setSelectedFile(file);
      } else {
        showToast({
          type: 'error',
          message: validation.error || 'Invalid file selected'
        });
        event.target.value = '';
      }
    }
  };

  const startAnalysis = async () => {
    if (!selectedFile) {
      showToast({
        type: 'warning',
        message: 'Please select a CSV file'
      });
      return;
    }

    if (analysisType === 'basic') {
      const validation = analyticsUtils.validateScanIds(scanIds);
      if (!validation.isValid) {
        showToast({
          type: 'error',
          message: validation.error || 'Invalid scan IDs format'
        });
        return;
      }
    }

    try {
      setIsAnalyzing(true);
      setAnalysisProgress(0);

      // Simulate progress during upload
      const progressInterval = setInterval(() => {
        setAnalysisProgress(prev => {
          if (prev < 90) return prev + 10;
          return prev;
        });
      }, 1000);

      let response;
      if (analysisType === 'full') {
        response = await analyticsService.startFullAnalysis(selectedFile);
      } else {
        response = await analyticsService.startBasicAnalysis(selectedFile, scanIds);
      }

      clearInterval(progressInterval);
      setAnalysisProgress(100);

      // Add analysis to running analyses
      const newRunningAnalyses = new Map(runningAnalyses);
      newRunningAnalyses.set(response.analysis_id, {
        analysis_id: response.analysis_id,
        status: 'pending',
        progress: 0
      });
      setRunningAnalyses(newRunningAnalyses);

      // Reset the form
      setSelectedFile(null);
      setScanIds('scan1,scan2,scan3');

      // Reload the list
      loadAnalyses();

      showToast({
        type: 'success',
        message: `✅ Analysis successfully launched! ID: ${response.analysis_id}`,
        duration: 6000
      });
    } catch (error: unknown) {
      console.error('Error launching analysis:', error);

      let errorMessage = 'Error launching analysis';

      if (error instanceof Error) {
        if ('code' in error) {
          const networkError = error as Error & { code?: string };
          if (networkError.code === 'ECONNABORTED') {
            errorMessage = 'Timeout - Analysis is taking longer than expected. It continues in the background.';
          } else if (networkError.code === 'ERR_NETWORK') {
            errorMessage = 'Network error - Check connection to Analytics server';
          }
        }

        if ('response' in error) {
          const httpError = error as Error & { response?: { status?: number; data?: { message?: string } } };
          if (httpError.response?.status) {
            errorMessage = `Server error: ${httpError.response.status} - ${httpError.response.data?.message || 'Unknown error'}`;
          }
        }
      }

      showToast({
        type: 'error',
        message: errorMessage,
        duration: 6000
      });
    } finally {
      setIsAnalyzing(false);
      setAnalysisProgress(0);
    }
  };

  const viewReport = async (analysis: AnalysisListItem) => {
    try {
      const report = await analyticsService.getAnalysisReport(analysis.analysis_id);
      setReportData(report);
      setSelectedAnalysis(analysis);

      // Extract vulnerabilities from the report
      if (report && (report.report || report.vulnerability_assessment_report)) {
        const extractedVulns = analyticsUtils.extractVulnerabilities(report);
        setVulnerabilities(extractedVulns);
        setFilteredVulnerabilities(extractedVulns);
      }

      setShowReportModal(true);
    } catch (error) {
      console.error('Error retrieving report:', error);
      showAlert({
        type: 'error',
        title: 'Report Error',
        message: 'Error retrieving report'
      });
    }
  };

  // Filter vulnerabilities with debouncing to reduce unnecessary re-renders
  useEffect(() => {
    const debounceTimer = setTimeout(() => {
      let filtered = vulnerabilities;

      // Filter by severity
      if (severityFilter !== 'all') {
        filtered = filtered.filter(vuln =>
          vuln.basic_info?.severity?.toLowerCase() === severityFilter.toLowerCase()
        );
      }

      // Filter by search term
      if (searchTerm) {
        const term = searchTerm.toLowerCase();
        filtered = filtered.filter(vuln => {
          const basicInfo = vuln.basic_info || {};
          const aiAnalysis = vuln.ai_analysis?.vulnerability_analysis || {};

          return (
            basicInfo.name?.toLowerCase().includes(term) ||
            basicInfo.target?.toLowerCase().includes(term) ||
            basicInfo.cve?.toLowerCase().includes(term) ||
            aiAnalysis.summary?.toLowerCase().includes(term)
          );
        });
      }

      setFilteredVulnerabilities(filtered);
    }, 300); // 300ms debounce delay

    return () => clearTimeout(debounceTimer);
  }, [vulnerabilities, severityFilter, searchTerm]);

  const handleVulnerabilityDetails = (vulnerability: VulnerabilityInfo) => {
    setSelectedVulnerability(vulnerability);
    setShowVulnerabilityModal(true);
  };



  const checkAnalysisStatus = async () => {
    if (!statusCheckId.trim()) {
      showToast({
        type: 'warning',
        message: 'Please enter an analysis ID'
      });
      return;
    }

    setCheckingStatus(true);
    setStatusResult(null);

    try {
      const status = await analyticsService.getAnalysisStatus(statusCheckId.trim());
      setStatusResult(status);
    } catch (error) {
      console.error('Error checking status:', error);
      setStatusResult({
        error: true,
        message: 'Unable to retrieve analysis status'
      });
    } finally {
      setCheckingStatus(false);
    }
  };

  const deleteAnalysis = async (analysisId: string) => {
    const confirmed = await showConfirm({
      type: 'danger',
      title: 'Delete Analysis',
      message: 'Are you sure you want to delete this analysis?\n\nThis action cannot be undone.',
      confirmText: 'Delete',
      cancelText: 'Cancel'
    });

    if (!confirmed) {
      return;
    }

    try {
      await analyticsService.deleteAnalysis(analysisId);
      loadAnalyses();
      showAlert({
        type: 'success',
        title: 'Analysis Deleted',
        message: 'Analysis deleted successfully'
      });
    } catch (error) {
      console.error('Error during deletion:', error);
      showAlert({
        type: 'error',
        title: 'Deletion Error',
        message: 'Error during deletion'
      });
    }
  };



  const retryAnalysis = async (analysisId: string) => {
    try {
      const response = await analyticsService.retryAnalysis(analysisId);

      // Add to running analyses
      const newRunningAnalyses = new Map(runningAnalyses);
      newRunningAnalyses.set(response.analysis_id, {
        analysis_id: response.analysis_id,
        status: 'pending',
        progress: 0
      });
      setRunningAnalyses(newRunningAnalyses);

      loadAnalyses();
      showAlert({
        type: 'success',
        title: 'Analysis Restarted',
        message: 'Analysis restarted successfully'
      });
    } catch (error) {
      console.error('Error during restart:', error);
      showAlert({
        type: 'error',
        title: 'Restart Error',
        message: 'Error during restart'
      });
    }
  };

  const exportAnalyses = async (format: 'csv' | 'json') => {
    try {
      const blob = await analyticsService.exportAnalyses(format);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `analyses_export.${format}`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error during export:', error);
      showAlert({
        type: 'error',
        title: 'Export Error',
        message: 'Error during export'
      });
    }
  };

  return (
    <div className="space-y-8">
      {/* Header - 3D Cyberpunk Design with AI effects
          Performance Note: Complex animations and 3D effects may impact performance on lower-end devices.
          Consider adding a performance mode toggle to reduce visual complexity if needed. */}
      <div className="relative group perspective-1000 mb-12 overflow-hidden">
        {/* Main 3D container */}
        <div className="relative transform-gpu transition-all duration-700 group-hover:rotateX-2 group-hover:rotateY-1 group-hover:translateZ-4">
          {/* Complex holographic background - Cyan/purple gradient */}


          {/* Main card with 3D effects */}
          <div className="relative bg-gradient-to-br from-gray-900/95 via-cyan-900/90 to-gray-900/95 backdrop-blur-2xl border-2 border-cyan-500/50 rounded-3xl p-8 shadow-2xl overflow-hidden transform-gpu transition-all duration-500 hover:shadow-cyan-500/25 hover:shadow-2xl">

            {/* Matrix scan effect */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-cyan-500/20 to-transparent h-full animate-pulse transform translateY-full group-hover:translateY-0 transition-transform duration-2000" />
              {[...Array(20)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-px h-full bg-gradient-to-b from-transparent via-cyan-400/30 to-transparent animate-pulse"
                  style={{
                    left: `${i * 5}%`,
                    animationDelay: `${i * 0.1}s`,
                    animationDuration: `${2 + Math.random()}s`
                  }}
                />
              ))}
            </div>

            {/* Animated neural network background */}
            <div className="absolute inset-0 opacity-15">
              <svg viewBox="0 0 400 300" className="w-full h-full">
                <defs>
                  <radialGradient id="neuralGlowHeader" cx="50%" cy="50%" r="50%">
                    <stop offset="0%" stopColor="#06b6d4" stopOpacity="0.8" />
                    <stop offset="50%" stopColor="#8b5cf6" stopOpacity="0.6" />
                    <stop offset="100%" stopColor="#ec4899" stopOpacity="0.4" />
                  </radialGradient>
                  <filter id="neuralBlurHeader">
                    <feGaussianBlur stdDeviation="2" />
                  </filter>
                </defs>
                {[...Array(12)].map((_, i) => (
                  <g key={i}>
                    <circle
                      cx={50 + (i % 4) * 100}
                      cy={50 + Math.floor(i / 4) * 100}
                      r="3"
                      fill="url(#neuralGlowHeader)"
                      filter="url(#neuralBlurHeader)"
                      className="animate-pulse"
                      style={{ animationDelay: `${i * 0.2}s` }}
                    />
                    {i < 11 && (
                      <line
                        x1={50 + (i % 4) * 100}
                        y1={50 + Math.floor(i / 4) * 100}
                        x2={50 + ((i + 1) % 4) * 100}
                        y2={50 + Math.floor((i + 1) / 4) * 100}
                        stroke="url(#neuralGlowHeader)"
                        strokeWidth="1"
                        opacity="0.6"
                        filter="url(#neuralBlurHeader)"
                        className="animate-pulse"
                        style={{ animationDelay: `${i * 0.15}s` }}
                      />
                    )}
                  </g>
                ))}
              </svg>
            </div>

            {/* Content */}
            <div className="relative z-10">
              <div className="flex items-center space-x-6 mb-8">
                {/* AI Brain Icon with glow effect */}
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-500 to-purple-600 rounded-2xl blur-lg opacity-75 animate-pulse"></div>
                  <div className="relative w-20 h-20 bg-gradient-to-br from-cyan-500 via-purple-600 to-pink-500 rounded-2xl flex items-center justify-center shadow-2xl transform-gpu transition-all duration-300 hover:scale-110 hover:rotate-3">
                    <Brain className="w-10 h-10 text-white animate-pulse" />
                    {/* Orbiting particles */}
                    <div className="absolute inset-0">
                      {[...Array(3)].map((_, i) => (
                        <div
                          key={i}
                          className="absolute w-2 h-2 bg-cyan-400 rounded-full opacity-60"
                          style={{
                            animation: `orbit 3s linear infinite`,
                            animationDelay: `${i * 1}s`,
                            transformOrigin: '40px 40px'
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </div>

                {/* Title with glitch effect */}
                <div className="flex-1">
                  <h1 className="text-5xl font-bold bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-2 relative">
                    AI Analytics
                    {/* Glitch overlay */}
                    <span className="absolute inset-0 bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent opacity-0 animate-pulse">
                      AI Analytics
                    </span>
                  </h1>
                  <p className="text-xl text-gray-300 font-light tracking-wide">
                    Advanced Analysis with Artificial Intelligence
                  </p>
                  {/* Animated underline */}
                  <div className="mt-2 h-1 bg-gradient-to-r from-cyan-500 via-purple-500 to-pink-500 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-700 origin-left"></div>
                </div>
              </div>

              {/* Export and refresh buttons with enhanced effects */}
              <div className="flex flex-wrap gap-4">
                <button
                  onClick={() => exportAnalyses('csv')}
                  className="relative group/btn flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-green-600 via-emerald-600 to-teal-600 hover:from-green-500 hover:via-emerald-500 hover:to-teal-500 text-white rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-green-500/25 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 opacity-0 group-hover/btn:opacity-20 transition-opacity duration-300"></div>
                  <Download className="w-5 h-5 relative z-10" />
                  <span className="relative z-10 font-medium">Export CSV</span>
                </button>
                <button
                  onClick={() => exportAnalyses('json')}
                  className="relative group/btn flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-500 hover:via-indigo-500 hover:to-purple-500 text-white rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-indigo-400 opacity-0 group-hover/btn:opacity-20 transition-opacity duration-300"></div>
                  <Download className="w-5 h-5 relative z-10" />
                  <span className="relative z-10 font-medium">Export JSON</span>
                </button>
                <button
                  onClick={loadAnalyses}
                  className="relative group/btn flex items-center space-x-3 px-6 py-3 bg-gradient-to-r from-gray-600 via-slate-600 to-gray-700 hover:from-gray-500 hover:via-slate-500 hover:to-gray-600 text-white rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-gray-500/25 overflow-hidden"
                >
                  <div className="absolute inset-0 bg-gradient-to-r from-gray-400 to-slate-400 opacity-0 group-hover/btn:opacity-20 transition-opacity duration-300"></div>
                  <RefreshCw className="w-5 h-5 relative z-10 group-hover/btn:animate-spin" />
                  <span className="relative z-10 font-medium">Refresh</span>
                </button>
              </div>
            </div>

            {/* Floating particles */}
            <div className="absolute inset-0 pointer-events-none">
              {[...Array(8)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-1 h-1 bg-cyan-400 rounded-full opacity-60 animate-bounce"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    animationDelay: `${Math.random() * 2}s`,
                    animationDuration: `${2 + Math.random() * 2}s`
                  }}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Statistics - 3D Cyberpunk Design with blue gradient */}
      {statistics && (
        <div className="relative group perspective-1000 mb-12 overflow-hidden">
          {/* Main 3D container */}
          <div className="relative transform-gpu transition-all duration-700 group-hover:rotateX-2 group-hover:rotateY-1 group-hover:translateZ-4">
            {/* Complex holographic background - Blue/indigo gradient */}


            {/* Main card with 3D effects */}
            <div className="relative bg-gradient-to-br from-gray-900/95 via-blue-900/90 to-gray-900/95 backdrop-blur-2xl border-2 border-blue-500/50 rounded-3xl p-8 shadow-2xl overflow-hidden transform-gpu transition-all duration-500 hover:shadow-blue-500/25 hover:shadow-2xl">

              {/* Matrix scan effect */}
              <div className="absolute inset-0 opacity-10">
                <div className="absolute inset-0 bg-gradient-to-b from-transparent via-blue-500/20 to-transparent h-full animate-pulse transform translateY-full group-hover:translateY-0 transition-transform duration-2000" />
                {[...Array(20)].map((_, i) => (
                  <div
                    key={i}
                    className="absolute w-px h-full bg-gradient-to-b from-transparent via-blue-400/30 to-transparent animate-pulse"
                    style={{
                      left: `${i * 5}%`,
                      animationDelay: `${i * 0.1}s`,
                      animationDuration: `${2 + Math.random()}s`
                    }}
                  />
                ))}
              </div>

              {/* Animated neural network background */}
              <div className="absolute inset-0 opacity-15">
                <svg viewBox="0 0 400 300" className="w-full h-full">
                  <defs>
                    <radialGradient id="neuralGlowStats" cx="50%" cy="50%" r="50%">
                      <stop offset="0%" stopColor="#3b82f6" stopOpacity="0.8" />
                      <stop offset="50%" stopColor="#6366f1" stopOpacity="0.6" />
                      <stop offset="100%" stopColor="#06b6d4" stopOpacity="0.4" />
                    </radialGradient>
                    <filter id="neuralBlurStats">
                      <feGaussianBlur stdDeviation="2" />
                    </filter>
                  </defs>

                  {/* Network nodes */}
                  {[...Array(25)].map((_, i) => {
                    const x = 50 + (i % 5) * 80;
                    const y = 50 + Math.floor(i / 5) * 50;
                    return (
                      <g key={i}>
                        <circle
                          cx={x}
                          cy={y}
                          r="4"
                          fill="url(#neuralGlowStats)"
                          filter="url(#neuralBlurStats)"
                          className="animate-pulse"
                          style={{ animationDelay: `${i * 0.2}s` }}
                        />
                        {i < 20 && (
                          <line
                            x1={x}
                            y1={y}
                            x2={x + 80}
                            y2={y + 50}
                            stroke="#3b82f6"
                            strokeWidth="1"
                            opacity="0.3"
                            className="animate-pulse"
                            style={{ animationDelay: `${i * 0.1}s` }}
                          />
                        )}
                      </g>
                    );
                  })}
                </svg>
              </div>

              {/* Floating quantum particles */}
              {[...Array(30)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-1 h-1 rounded-full animate-pulse"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                    backgroundColor: ['#3b82f6', '#6366f1', '#06b6d4', '#1e40af'][Math.floor(Math.random() * 4)],
                    animationDelay: `${Math.random() * 3}s`,
                    animationDuration: `${1 + Math.random() * 2}s`,
                    boxShadow: `0 0 8px currentColor`,
                    transform: `scale(${0.5 + Math.random() * 1.5})`
                  }}
                />
              ))}

              {/* Section title */}
              <div className="relative z-10 mb-8 text-center">
                <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-400 via-indigo-400 to-cyan-400 bg-clip-text text-transparent font-mono">
                  ◉ NEURAL DASHBOARD ◉
                </h2>
                <p className="text-blue-300/80 font-mono text-sm mt-2 flex items-center justify-center space-x-2">
                  <span className="w-2 h-2 bg-blue-400 rounded-full animate-ping" />
                  <span>Real-time metrics</span>
                  <span className="w-2 h-2 bg-blue-400 rounded-full animate-ping" />
                </p>
              </div>

              {/* Statistics grid */}
              <div className="relative z-10 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs">Total</p>
                      <p className="text-xl font-bold text-white">{statistics.total_analyses}</p>
                    </div>
                    <Activity className="h-6 w-6 text-blue-400" />
                  </div>
                </div>
                <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs">Completed</p>
                      <p className="text-xl font-bold text-green-400">{statistics.completed_analyses}</p>
                    </div>
                    <CheckCircle className="h-6 w-6 text-green-400" />
                  </div>
                </div>
                <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs">Failed</p>
                      <p className="text-xl font-bold text-red-400">{statistics.failed_analyses}</p>
                    </div>
                    <AlertTriangle className="h-6 w-6 text-red-400" />
                  </div>
                </div>
                <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs">In Progress</p>
                      <p className="text-xl font-bold text-yellow-400">{statistics.pending_analyses}</p>
                    </div>
                    <Clock className="h-6 w-6 text-yellow-400" />
                  </div>
                </div>
                <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs">Full Reports</p>
                      <p className="text-xl font-bold text-purple-400">{statistics.full_reports}</p>
                    </div>
                    <FileText className="h-6 w-6 text-purple-400" />
                  </div>
                </div>
                <div className="bg-gray-700/30 border border-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-400 text-xs">Basic Reports</p>
                      <p className="text-xl font-bold text-cyan-400">{statistics.basic_reports}</p>
                    </div>
                    <Zap className="w-6 h-6 text-cyan-400" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}



      {/* Analysis status check - 3D Cyberpunk Design with green gradient */}
      <div className="relative group perspective-1000 mb-12 overflow-hidden">
        {/* Main 3D container */}
        <div className="relative transform-gpu transition-all duration-700 group-hover:rotateX-2 group-hover:rotateY-1 group-hover:translateZ-4">
          {/* Complex holographic background - Green/emerald gradient */}


          {/* Main card with 3D effects */}
          <div className="relative bg-gradient-to-br from-gray-900/95 via-emerald-900/90 to-gray-900/95 backdrop-blur-2xl border-2 border-emerald-500/50 rounded-3xl p-8 shadow-2xl overflow-hidden transform-gpu transition-all duration-500 hover:shadow-emerald-500/25 hover:shadow-2xl">

            {/* Matrix scan effect */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-emerald-500/20 to-transparent h-full animate-pulse transform translateY-full group-hover:translateY-0 transition-transform duration-2000" />
              {[...Array(20)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-px h-full bg-gradient-to-b from-transparent via-emerald-400/30 to-transparent animate-pulse"
                  style={{
                    left: `${i * 5}%`,
                    animationDelay: `${i * 0.1}s`,
                    animationDuration: `${2 + Math.random()}s`
                  }}
                />
              ))}
            </div>

            {/* Animated neural network background */}
            <div className="absolute inset-0 opacity-15">
              <svg viewBox="0 0 400 300" className="w-full h-full">
                <defs>
                  <radialGradient id="neuralGlowScanner" cx="50%" cy="50%" r="50%">
                    <stop offset="0%" stopColor="#10b981" stopOpacity="0.8" />
                    <stop offset="50%" stopColor="#059669" stopOpacity="0.6" />
                    <stop offset="100%" stopColor="#14b8a6" stopOpacity="0.4" />
                  </radialGradient>
                  <filter id="neuralBlurScanner">
                    <feGaussianBlur stdDeviation="2" />
                  </filter>
                </defs>

                {/* Network nodes */}
                {[...Array(25)].map((_, i) => {
                  const x = 50 + (i % 5) * 80;
                  const y = 50 + Math.floor(i / 5) * 50;
                  return (
                    <g key={i}>
                      <circle
                        cx={x}
                        cy={y}
                        r="4"
                        fill="url(#neuralGlowScanner)"
                        filter="url(#neuralBlurScanner)"
                        className="animate-pulse"
                        style={{ animationDelay: `${i * 0.2}s` }}
                      />
                      {i < 20 && (
                        <line
                          x1={x}
                          y1={y}
                          x2={x + 80}
                          y2={y + 50}
                          stroke="#10b981"
                          strokeWidth="1"
                          opacity="0.3"
                          className="animate-pulse"
                          style={{ animationDelay: `${i * 0.1}s` }}
                        />
                      )}
                    </g>
                  );
                })}
              </svg>
            </div>

            {/* Floating quantum particles */}
            {[...Array(30)].map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 rounded-full animate-pulse"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  backgroundColor: ['#10b981', '#059669', '#14b8a6', '#0d9488'][Math.floor(Math.random() * 4)],
                  animationDelay: `${Math.random() * 3}s`,
                  animationDuration: `${1 + Math.random() * 2}s`,
                  boxShadow: `0 0 8px currentColor`,
                  transform: `scale(${0.5 + Math.random() * 1.5})`
                }}
              />
            ))}

            {/* Corner radar scan effect */}
            <div className="absolute top-6 right-6 w-20 h-20 opacity-20">
              <div className="absolute inset-0 border-2 border-emerald-400/40 rounded-full animate-ping" />
              <div className="absolute inset-2 border border-emerald-400/60 rounded-full animate-ping" style={{ animationDelay: '0.5s' }} />
              <div className="absolute inset-4 border border-emerald-400/80 rounded-full animate-ping" style={{ animationDelay: '1s' }} />

              {/* Rotating scan line */}
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-8 h-px bg-gradient-to-r from-transparent via-emerald-400 to-transparent animate-spin" style={{ animationDuration: '2s' }} />
              </div>
            </div>

            {/* Title with 3D scanner logo */}
            <div className="relative z-10 mb-8">
              <div className="flex items-center space-x-4">
                {/* 3D scanner logo */}
                <div className="relative group/scanner">
                  <div className="w-16 h-16 relative transform-gpu transition-all duration-1000 group-hover/scanner:rotateY-180">
                    {/* Scanner frame */}
                    <div className="absolute inset-0 border-2 border-emerald-400/50 rounded-xl transform rotate-45" />
                    <div className="absolute inset-2 border border-green-400/50 rounded-lg transform -rotate-45" />

                    {/* Luminous center */}
                    <div className="absolute inset-4 bg-gradient-to-br from-emerald-400 to-green-500 rounded-lg flex items-center justify-center shadow-lg shadow-emerald-500/50">
                      <Search className="w-6 h-6 text-white animate-pulse" />
                    </div>

                    {/* Scan lines */}
                    <div className="absolute inset-0 overflow-hidden rounded-xl">
                      <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-emerald-400 to-transparent animate-pulse" />
                      <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-emerald-400 to-transparent animate-pulse" style={{ animationDelay: '1s' }} />
                    </div>
                  </div>

                  {/* Scanner halo */}
                  <div className="absolute -inset-2 bg-gradient-to-r from-emerald-500/20 via-green-500/20 to-teal-500/20 rounded-xl blur-lg animate-pulse" />
                </div>

                {/* Cyberpunk title */}
                <div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-emerald-400 via-green-400 to-teal-400 bg-clip-text text-transparent font-mono flex items-center">
                    <span className="inline-block animate-pulse text-emerald-400 mr-2">◉</span>
                    AI STATUS SCANNER
                  </h2>
                  <p className="text-emerald-300/80 font-mono text-sm mt-1 flex items-center space-x-2">
                    <span className="w-2 h-2 bg-emerald-400 rounded-full animate-ping" />
                    <span>Real-time neural analysis</span>
                    <Zap className="w-4 h-4 animate-pulse" />
                  </p>
                </div>
              </div>
            </div>

            {/* Scanner content */}
            <div className="relative z-10">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Analysis ID
                  </label>
                  <div className="flex space-x-3">
                    <input
                      type="text"
                      value={statusCheckId}
                      onChange={(e) => setStatusCheckId(e.target.value)}
                      placeholder="Enter analysis ID (e.g. abc123...)"
                      className="flex-1 px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-green-500 transition-colors"
                    />
                    <button
                      onClick={checkAnalysisStatus}
                      disabled={checkingStatus || !statusCheckId.trim()}
                      className="flex items-center space-x-2 px-6 py-3 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 disabled:from-gray-600 disabled:to-gray-700 text-white rounded-xl transition-all duration-200 transform hover:scale-105 disabled:scale-100"
                    >
                      {checkingStatus ? (
                        <RefreshCw className="w-5 h-5 animate-spin" />
                      ) : (
                        <Search className="w-5 h-5" />
                      )}
                      <span>{checkingStatus ? 'Checking...' : 'Check'}</span>
                    </button>
                  </div>
                </div>

                {/* Verification result */}
                {statusResult && (
                  <div className={`p-4 rounded-xl border ${
                    statusResult.error
                      ? 'bg-red-500/20 border-red-500/30'
                      : 'bg-green-500/20 border-green-500/30'
                  }`}>
                    {statusResult.error ? (
                      <div className="text-red-300">
                        <h3 className="font-semibold mb-2">❌ Error</h3>
                        <p>{statusResult.message}</p>
                      </div>
                    ) : (
                      <div className="text-green-300">
                        <h3 className="font-semibold mb-3">📊 Analysis Status</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                          <div>
                            <span className="text-gray-400">ID:</span>
                            <span className="ml-2 font-mono">{statusResult.analysis_id || statusCheckId}</span>
                          </div>
                          <div>
                            <span className="text-gray-400">Statut:</span>
                            <span className={`ml-2 px-2 py-1 rounded text-xs ${
                              statusResult.status === 'completed' ? 'bg-green-500/30 text-green-300' :
                              statusResult.status === 'running' ? 'bg-blue-500/30 text-blue-300' :
                              statusResult.status === 'pending' ? 'bg-yellow-500/30 text-yellow-300' :
                              'bg-red-500/30 text-red-300'
                            }`}>
                              {statusResult.status || 'Inconnu'}
                            </span>
                          </div>
                          {statusResult.progress !== undefined && (
                            <div>
                              <span className="text-gray-400">Progress:</span>
                              <span className="ml-2">{statusResult.progress}%</span>
                            </div>
                          )}
                          {statusResult.started_at && (
                            <div>
                              <span className="text-gray-400">Started:</span>
                              <span className="ml-2">{new Date(statusResult.started_at).toLocaleString()}</span>
                            </div>
                          )}
                        </div>

                        {statusResult.status === 'completed' && (
                          <button
                            onClick={() => analyticsService.getAnalysisReport(statusCheckId.trim())}
                            className="mt-3 flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm rounded-xl transition-all duration-200"
                          >
                            <Download className="w-4 h-4" />
                            <span>Download Report</span>
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional spacing between Scanner and New Analysis */}
      <div className="h-16"></div>

      {/* New analysis form - 3D Cyberpunk Design */}
      <div className="relative group perspective-1000 mb-12 overflow-hidden">
        {/* Main 3D container */}
        <div className="relative transform-gpu transition-all duration-700 group-hover:rotateX-2 group-hover:rotateY-1 group-hover:translateZ-4">
          {/* Complex holographic background */}


          {/* Main card with 3D effects */}
          <div className="relative bg-gradient-to-br from-gray-900/95 via-purple-900/90 to-gray-900/95 backdrop-blur-2xl border-2 border-purple-500/50 rounded-3xl p-8 shadow-2xl overflow-hidden transform-gpu transition-all duration-500 hover:shadow-purple-500/25 hover:shadow-2xl">

            {/* Matrix scan effect */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute inset-0 bg-gradient-to-b from-transparent via-green-500/20 to-transparent h-full animate-pulse transform translateY-full group-hover:translateY-0 transition-transform duration-2000" />
              {[...Array(20)].map((_, i) => (
                <div
                  key={i}
                  className="absolute w-px h-full bg-gradient-to-b from-transparent via-cyan-400/30 to-transparent animate-pulse"
                  style={{
                    left: `${i * 5}%`,
                    animationDelay: `${i * 0.1}s`,
                    animationDuration: `${2 + Math.random()}s`
                  }}
                />
              ))}
            </div>

            {/* Animated neural network background */}
            <div className="absolute inset-0 opacity-15">
              <svg viewBox="0 0 400 300" className="w-full h-full">
                <defs>
                  <radialGradient id="neuralGlow" cx="50%" cy="50%" r="50%">
                    <stop offset="0%" stopColor="#8b5cf6" stopOpacity="0.8" />
                    <stop offset="50%" stopColor="#06b6d4" stopOpacity="0.6" />
                    <stop offset="100%" stopColor="#ec4899" stopOpacity="0.4" />
                  </radialGradient>
                  <filter id="neuralBlur">
                    <feGaussianBlur stdDeviation="2" />
                  </filter>
                </defs>

                {/* Network nodes */}
                {[...Array(25)].map((_, i) => {
                  const x = 50 + (i % 5) * 80;
                  const y = 50 + Math.floor(i / 5) * 50;
                  return (
                    <g key={i}>
                      <circle
                        cx={x}
                        cy={y}
                        r="4"
                        fill="url(#neuralGlow)"
                        filter="url(#neuralBlur)"
                        className="animate-pulse"
                        style={{ animationDelay: `${i * 0.2}s` }}
                      />
                      {i < 20 && (
                        <line
                          x1={x}
                          y1={y}
                          x2={x + 80}
                          y2={y + 50}
                          stroke="#06b6d4"
                          strokeWidth="1"
                          opacity="0.3"
                          className="animate-pulse"
                          style={{ animationDelay: `${i * 0.1}s` }}
                        />
                      )}
                    </g>
                  );
                })}
              </svg>
            </div>

            {/* Floating quantum particles */}
            {[...Array(30)].map((_, i) => (
              <div
                key={i}
                className="absolute w-1 h-1 rounded-full animate-pulse"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  backgroundColor: ['#8b5cf6', '#06b6d4', '#ec4899', '#10b981'][Math.floor(Math.random() * 4)],
                  animationDelay: `${Math.random() * 3}s`,
                  animationDuration: `${1 + Math.random() * 2}s`,
                  boxShadow: `0 0 8px currentColor`,
                  transform: `scale(${0.5 + Math.random() * 1.5})`
                }}
              />
            ))}

            {/* Header with 3D logo */}
            <div className="relative z-10 mb-8">
              <div className="flex items-center space-x-4">
                {/* Logo AI avec icône visible */}
                <div className="relative group/logo">
                  <div className="w-16 h-16 relative transform-gpu transition-all duration-700 group-hover/logo:scale-110 group-hover/logo:rotate-12">
                    {/* Background avec gradient animé */}
                    <div className="absolute inset-0 bg-gradient-to-br from-purple-500 via-cyan-500 to-pink-500 rounded-2xl shadow-lg shadow-purple-500/50 animate-pulse" />

                    {/* Icône centrale bien visible */}
                    <div className="absolute inset-0 flex items-center justify-center z-20">
                      <Brain className="w-10 h-10 text-white drop-shadow-2xl filter brightness-110" />
                    </div>

                    {/* Effet de brillance */}
                    <div className="absolute inset-0 bg-gradient-to-tr from-transparent via-white/20 to-transparent rounded-2xl" />
                  </div>

                  {/* Luminous halo */}
                  <div className="absolute -inset-2 bg-gradient-to-r from-purple-500/30 via-cyan-500/30 to-pink-500/30 rounded-3xl blur-lg animate-pulse" />
                </div>

                {/* Title with typing effect */}
                <div>
                  <h2 className="text-3xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-pink-400 bg-clip-text text-transparent font-mono flex items-center space-x-3">
                    <Brain className="w-8 h-8 text-purple-400 animate-pulse" />
                    <span>New AI Analysis</span>
                    <Sparkles className="w-6 h-6 text-cyan-400 animate-pulse" />
                  </h2>
                  <p className="text-cyan-300/80 font-mono text-sm mt-1 flex items-center space-x-2">
                    <span className="w-2 h-2 bg-green-400 rounded-full animate-pulse" />
                    <span>Neural system active</span>
                    <Cpu className="w-4 h-4 animate-pulse" />
                  </p>
                </div>
              </div>
            </div>

        <div className="relative z-10">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Analysis configuration */}
                <div className="space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Analysis Type
                    </label>
                    <div className="grid grid-cols-2 gap-3">
                      <button
                        onClick={() => setAnalysisType('full')}
                        className={`p-4 rounded-xl border transition-all duration-200 ${
                          analysisType === 'full'
                            ? 'bg-purple-600/20 border-purple-500 text-purple-300'
                            : 'bg-gray-700/30 border-gray-600 text-gray-400 hover:border-purple-500/50'
                        }`}
                      >
                        <div className="flex items-center space-x-2">
                          <Brain className="w-5 h-5" />
                          <span className="font-medium">Full Report</span>
                        </div>
                        <p className="text-xs mt-1">Complete analysis with AI</p>
                      </button>
                      <button
                        onClick={() => setAnalysisType('basic')}
                        className={`p-4 rounded-xl border transition-all duration-200 ${
                          analysisType === 'basic'
                            ? 'bg-cyan-600/20 border-cyan-500 text-cyan-300'
                            : 'bg-gray-700/30 border-gray-600 text-gray-400 hover:border-cyan-500/50'
                        }`}
                      >
                        <div className="flex items-center space-x-2">
                          <Zap className="w-5 h-5" />
                          <span className="font-medium">Basic Report</span>
                        </div>
                        <p className="text-xs mt-1">Filtering by scan IDs</p>
                      </button>
                    </div>
                  </div>

                  {/* File selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      CSV File
                    </label>
                    <div className="relative">
                      <input
                        type="file"
                        accept=".csv"
                        onChange={handleFileSelect}
                        className="hidden"
                        id="file-upload"
                      />
                      <label
                        htmlFor="file-upload"
                        className="flex items-center justify-center w-full p-6 border-2 border-dashed border-gray-600 rounded-xl cursor-pointer hover:border-purple-500/50 transition-all duration-200"
                      >
                        <div className="text-center">
                          <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                          <p className="text-gray-300">
                            {selectedFile ? selectedFile.name : 'Click to select a CSV file'}
                          </p>
                          {selectedFile && (
                            <p className="text-xs text-gray-500 mt-1">
                              {analyticsUtils.formatFileSize(selectedFile.size)}
                            </p>
                          )}
                        </div>
                      </label>
                    </div>
                  </div>

                  {/* Configuration for basic analysis */}
                  {analysisType === 'basic' && (
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Scan IDs (comma separated)
                      </label>
                      <input
                        type="text"
                        value={scanIds}
                        onChange={(e) => setScanIds(e.target.value)}
                        placeholder="scan1,scan2,scan3"
                        className="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-cyan-500 transition-colors"
                      />
                      <p className="text-xs text-gray-500 mt-1">
                        Example: scan1,scan2,scan3
                      </p>
                    </div>
                  )}
                </div>

                {/* Information and launch button */}
                <div className="space-y-6">
                  <div className="bg-gray-700/30 border border-gray-600 rounded-xl p-6">
                    <h3 className="text-lg font-semibold text-white mb-4">
                      {analysisType === 'full' ? 'Complete Analysis' : 'Basic Analysis'}
                    </h3>
                    <div className="space-y-3 text-sm">
                      {analysisType === 'full' ? (
                        <>
                          <div className="flex items-center space-x-2">
                            <Brain className="w-4 h-4 text-purple-400" />
                            <span className="text-gray-300">Complete analysis with AI</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-green-400" />
                            <span className="text-gray-300">Detailed report</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Sparkles className="w-4 h-4 text-yellow-400" />
                            <span className="text-gray-300">Automatic recommendations</span>
                          </div>
                        </>
                      ) : (
                        <>
                          <div className="flex items-center space-x-2">
                            <Filter className="w-4 h-4 text-cyan-400" />
                            <span className="text-gray-300">Filtering by scan IDs</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Zap className="w-4 h-4 text-yellow-400" />
                            <span className="text-gray-300">Quick Analysis</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <FileText className="w-4 h-4 text-cyan-400" />
                            <span className="text-gray-300">Basic Report</span>
                          </div>
                        </>
                      )}
                    </div>
                  </div>

                  <div className="space-y-3">
                    {/* Progress bar during analysis */}
                    {isAnalyzing && (
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-300">Analysis Progress</span>
                          <span className="text-white font-semibold">{analysisProgress}%</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-purple-500 to-cyan-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${analysisProgress}%` }}
                          ></div>
                        </div>
                        <p className="text-xs text-gray-400 text-center">
                          {analysisProgress < 30 ? 'Preparing file...' :
                           analysisProgress < 60 ? 'AI analysis in progress...' :
                           analysisProgress < 90 ? 'Generating report...' :
                           'Finalizing...'}
                        </p>
                      </div>
                    )}

                    <button
                      onClick={startAnalysis}
                      disabled={!selectedFile || isAnalyzing}
                      className={`w-full flex items-center justify-center space-x-3 px-6 py-4 rounded-xl font-semibold transition-all duration-200 transform hover:scale-105 ai-button-shine ${
                        !selectedFile || isAnalyzing
                          ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                          : analysisType === 'full'
                          ? 'bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white shadow-lg ai-glow'
                          : 'bg-gradient-to-r from-cyan-600 to-blue-600 hover:from-cyan-700 hover:to-blue-700 text-white shadow-lg ai-glow'
                      }`}
                    >
                      {isAnalyzing ? (
                        <>
                          <RefreshCw className="w-5 h-5 animate-spin" />
                          <span>Analysis in progress... (may take several minutes)</span>
                        </>
                      ) : (
                        <>
                          <Play className="w-5 h-5" />
                          <span>Launch Analysis</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Additional spacing between New Analysis and Analysis History */}
      <div className="h-16"></div>

      {/* Analysis list */}
      <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl mb-12">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-400 via-cyan-400 to-purple-400 bg-clip-text text-transparent mb-6 flex items-center">
          <div className="relative mr-3">
            <FileText className="w-6 h-6 text-blue-400" />
            <div className="absolute -inset-1 bg-blue-500/20 rounded-lg blur animate-pulse" />
          </div>
          Analysis History ({analyses.length})
          <div className="ml-2 flex space-x-1">
            {[...Array(3)].map((_, i) => (
              <div
                key={i}
                className="w-1 h-1 bg-cyan-400 rounded-full animate-pulse"
                style={{
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: '1.5s'
                }}
              />
            ))}
          </div>
        </h2>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <RefreshCw className="w-8 h-8 text-blue-400 animate-spin" />
            <span className="ml-3 text-gray-300">Loading analyses...</span>
          </div>
        ) : analyses.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 text-gray-500 mx-auto mb-4" />
            <p className="text-gray-400 text-lg">No analysis found</p>
            <p className="text-gray-500 text-sm">Launch your first analysis to get started</p>
          </div>
        ) : (
          <div className="space-y-4">
            {analyses.map((analysis) => (
              <div key={analysis.analysis_id} className="relative bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 shadow-xl overflow-hidden group hover:border-purple-500/50 transition-all duration-300 transform hover:scale-[1.01]">
                {/* Subtle glow effect on hover */}
                <div className="absolute inset-0 bg-gradient-to-r from-purple-500/5 via-cyan-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                {/* Animated status indicator */}
                <div className="absolute top-4 right-4">
                  {analysis.status === 'completed' && (
                    <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse shadow-lg shadow-green-400/50" />
                  )}
                  {analysis.status === 'failed' && (
                    <div className="w-3 h-3 bg-red-400 rounded-full animate-pulse shadow-lg shadow-red-400/50" />
                  )}
                  {(analysis.status === 'running' || analysis.status === 'processing') && (
                    <div className="w-3 h-3 bg-cyan-400 rounded-full animate-ping shadow-lg shadow-cyan-400/50" />
                  )}
                </div>

                {/* Card content */}
                <div className="relative z-10">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-4 mb-2">
                      <h3 className="text-lg font-semibold text-white">{analysis.analysis_id}</h3>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        analyticsUtils.formatStatus(analysis.status).bgColor
                      } ${analyticsUtils.formatStatus(analysis.status).color}`}>
                        {analyticsUtils.formatStatus(analysis.status).text}
                      </span>
                      <span className={`px-3 py-1 rounded-full text-xs font-medium ${
                        analysis.generate_full_report
                          ? 'bg-purple-500/20 border-purple-500/30 text-purple-300'
                          : 'bg-cyan-500/20 border-cyan-500/30 text-cyan-300'
                      }`}>
                        {analysis.generate_full_report ? 'Full Report' : 'Basic Report'}
                      </span>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <p className="text-gray-400">Created </p>
                        <p className="text-white">{new Date(analysis.created_at).toLocaleString()}</p>
                      </div>
                      {analysis.completed_at && (
                        <div>
                          <p className="text-gray-400">Ended </p>
                          <p className="text-white">{new Date(analysis.completed_at).toLocaleString()}</p>
                        </div>
                      )}
                      {analysis.file_info && (
                        <div>
                          <p className="text-gray-400">File</p>
                          <p className="text-white">{analysis.file_info.filename}</p>
                          <p className="text-gray-500 text-xs">{analyticsUtils.formatFileSize(analysis.file_info.size)}</p>
                        </div>
                      )}
                      {analysis.scan_ids && (
                        <div>
                          <p className="text-gray-400">Scan IDs</p>
                          <p className="text-white text-xs">{analysis.scan_ids.join(', ')}</p>
                        </div>
                      )}
                    </div>

                    {analysis.created_at && analysis.completed_at && (
                      <div className="mt-2">
                        <p className="text-gray-400 text-xs">
                          Duration: {analyticsUtils.calculateDuration(analysis.created_at, analysis.completed_at)}
                        </p>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center space-x-3 ml-6">
                    {analysis.status === 'completed' && (
                      <button
                        onClick={() => viewReport(analysis)}
                        className="relative flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white text-sm rounded-xl transition-all duration-200 transform hover:scale-105 ai-button-shine overflow-hidden group"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500" />
                        <Eye className="w-4 h-4 relative z-10" />
                        <span className="relative z-10">View Report</span>
                      </button>
                    )}

                    {(analysis.status === 'failed' || analysis.status === 'error') && (
                      <button
                        onClick={() => retryAnalysis(analysis.analysis_id)}
                        className="relative flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white text-sm rounded-xl transition-all duration-200 transform hover:scale-105 ai-button-shine overflow-hidden group"
                      >
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500" />
                        <RotateCcw className="w-4 h-4 relative z-10 group-hover:animate-spin" />
                        <span className="relative z-10">Restart</span>
                      </button>
                    )}

                    <button
                      onClick={() => deleteAnalysis(analysis.analysis_id)}
                      className="relative flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white text-sm rounded-xl transition-all duration-200 transform hover:scale-105 ai-button-shine overflow-hidden group"
                    >
                      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-500" />
                      <Trash2 className="w-4 h-4 relative z-10 group-hover:animate-bounce" />
                      <span className="relative z-10">Delete</span>
                    </button>
                  </div>
                </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Modal to display reports */}
      {showReportModal && selectedAnalysis && reportData && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 border border-gray-700 rounded-2xl max-w-7xl w-full max-h-[95vh] overflow-hidden">
            {/* Modal header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div>
                <h3 className="text-xl font-bold text-white">Vulnerability Analysis Report</h3>
                <p className="text-gray-400 text-sm">{selectedAnalysis.analysis_id}</p>
              </div>
              <div className="flex items-center space-x-3">
                {/* Display controls */}
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setViewMode('grid')}
                    className={`p-2 rounded-lg transition-colors ${
                      viewMode === 'grid' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-400 hover:text-white'
                    }`}
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setViewMode('list')}
                    className={`p-2 rounded-lg transition-colors ${
                      viewMode === 'list' ? 'bg-purple-600 text-white' : 'bg-gray-700 text-gray-400 hover:text-white'
                    }`}
                  >
                    <List className="w-4 h-4" />
                  </button>
                </div>

                <button
                  onClick={() => {
                    setShowReportModal(false);
                    setSelectedAnalysis(null);
                    setReportData(null);
                    setVulnerabilities([]);
                    setFilteredVulnerabilities([]);
                  }}
                  className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-gray-400" />
                </button>
              </div>
            </div>

            {/* Modal content */}
            <div className="overflow-y-auto max-h-[calc(95vh-120px)]">
              <div className="p-6 space-y-6">
                {/* Vulnerability statistics */}
                {reportData.report && (
                  <VulnerabilityStats
                    vulnerabilities={vulnerabilities}
                    metadata={reportData.report.vulnerability_assessment_report?.metadata}
                  />
                )}

                {/* Filters and search */}
                {vulnerabilities.length > 0 && (
                  <div className="bg-gradient-to-br from-gray-800/60 to-gray-900/60 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6">
                    <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                      <h4 className="text-lg font-bold text-white flex items-center">
                        <Filter className="w-5 h-5 mr-2 text-purple-400" />
                        Detected Vulnerabilities ({filteredVulnerabilities.length})
                      </h4>

                      <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
                        {/* Search */}
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                          <input
                            type="text"
                            placeholder="Search..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10 pr-4 py-2 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 transition-colors"
                          />
                        </div>

                        {/* Filter by severity */}
                        <select
                          value={severityFilter}
                          onChange={(e) => setSeverityFilter(e.target.value)}
                          className="px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:border-purple-500 transition-colors"
                        >
                          <option value="all">All severities</option>
                          <option value="critical">🔴 Critical</option>
                          <option value="high">🟠 High</option>
                          <option value="medium">🟡 Medium</option>
                          <option value="low">🟢 Low</option>
                        </select>
                      </div>
                    </div>
                  </div>
                )}

                {/* Vulnerability list */}
                {filteredVulnerabilities.length > 0 ? (
                  <div className={`${
                    viewMode === 'grid'
                      ? 'grid grid-cols-1 lg:grid-cols-2 gap-6'
                      : 'space-y-4'
                  }`}>
                    {filteredVulnerabilities.map((vulnerability, index) => (
                      <VulnerabilityCard
                        key={`${vulnerability.basic_info.scan_id}-${index}`}
                        vulnerability={vulnerability}
                        onViewDetails={handleVulnerabilityDetails}
                      />
                    ))}
                  </div>
                ) : vulnerabilities.length > 0 ? (
                  <div className="text-center py-12">
                    <Search className="w-16 h-16 text-gray-500 mx-auto mb-4" />
                    <p className="text-gray-400 text-lg">No vulnerabilities match the filters</p>
                    <button
                      onClick={() => {
                        setSeverityFilter('all');
                        setSearchTerm('');
                      }}
                      className="mt-4 px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-xl transition-colors"
                    >
                      Reset Filters
                    </button>
                  </div>
                ) : (
                  <div className="text-center py-12">
                    <Shield className="w-16 h-16 text-green-500 mx-auto mb-4" />
                    <p className="text-gray-400 text-lg">No vulnerabilities detected</p>
                    <p className="text-gray-500 text-sm">The system appears secure</p>
                  </div>
                )}

                {/* Actions */}
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-700">
                  <button
                    onClick={() => {
                      if (reportData && vulnerabilities.length > 0) {
                        downloadMarkdownReport(reportData, vulnerabilities);
                        showToast({
                          type: 'success',
                          message: 'Markdown report downloaded successfully!',
                          duration: 3000
                        });
                      } else {
                        showToast({
                          type: 'warning',
                          message: 'No vulnerabilities to export',
                          duration: 3000
                        });
                      }
                    }}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white rounded-xl transition-all duration-200"
                  >
                    <FileText className="w-4 h-4" />
                    <span>Download Markdown</span>
                  </button>
                  <button
                    onClick={() => {
                      const dataStr = JSON.stringify(reportData, null, 2);
                      const dataBlob = new Blob([dataStr], { type: 'application/json' });
                      const url = URL.createObjectURL(dataBlob);
                      const link = document.createElement('a');
                      link.href = url;
                      link.download = `report_${reportData.analysis_id}.json`;
                      link.click();
                      URL.revokeObjectURL(url);
                      showToast({
                        type: 'success',
                        message: 'JSON report downloaded successfully!',
                        duration: 3000
                      });
                    }}
                    className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl transition-all duration-200"
                  >
                    <Download className="w-4 h-4" />
                    <span>Download JSON</span>
                  </button>
                  <button
                    onClick={() => {
                      setShowReportModal(false);
                      setSelectedAnalysis(null);
                      setReportData(null);
                      setVulnerabilities([]);
                      setFilteredVulnerabilities([]);
                    }}
                    className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl transition-colors"
                  >
                    Close
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal for vulnerability details */}
      {showVulnerabilityModal && selectedVulnerability && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
          <div className="bg-gray-800 border border-gray-700 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            {/* Modal header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-700">
              <div>
                <h3 className="text-xl font-bold text-white">Vulnerability Details</h3>
                <p className="text-gray-400 text-sm">{selectedVulnerability.basic_info.name}</p>
              </div>
              <button
                onClick={() => {
                  setShowVulnerabilityModal(false);
                  setSelectedVulnerability(null);
                }}
                className="p-2 hover:bg-gray-700 rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-gray-400" />
              </button>
            </div>

            {/* Modal content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)] space-y-6">
              {/* Basic information */}
              <div className="bg-gray-700/30 border border-gray-600 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">Basic Information</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <p className="text-gray-400">Target</p>
                    <p className="text-white font-mono">{selectedVulnerability.basic_info.target}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Severity</p>
                    <span className={`px-2 py-1 rounded text-xs ${
                      analyticsUtils.formatSeverity(selectedVulnerability.basic_info.severity).bgColor
                    } ${analyticsUtils.formatSeverity(selectedVulnerability.basic_info.severity).color}`}>
                      {analyticsUtils.formatSeverity(selectedVulnerability.basic_info.severity).text}
                    </span>
                  </div>
                  {selectedVulnerability.basic_info.cve && (
                    <div>
                      <p className="text-gray-400">CVE</p>
                      <p className="text-white">{selectedVulnerability.basic_info.cve}</p>
                    </div>
                  )}
                  {selectedVulnerability.basic_info.port && (
                    <div>
                      <p className="text-gray-400">Port</p>
                      <p className="text-white">{selectedVulnerability.basic_info.port}</p>
                    </div>
                  )}
                  {selectedVulnerability.basic_info.service && (
                    <div>
                      <p className="text-gray-400">Service</p>
                      <p className="text-white">{selectedVulnerability.basic_info.service}</p>
                    </div>
                  )}
                  <div>
                    <p className="text-gray-400">Security Score</p>
                    <p className="text-white font-bold">{selectedVulnerability.basic_info.security_score || 'N/A'}</p>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className="bg-gray-700/30 border border-gray-600 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-white mb-4">Description</h4>
                <p className="text-gray-300">{selectedVulnerability.basic_info.description}</p>
              </div>

              {/* AI Analysis */}
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-blue-300 mb-4">AI Analysis</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-semibold text-white mb-2">Summary</h5>
                    <p className="text-gray-300 text-sm">{selectedVulnerability.ai_analysis.vulnerability_analysis.summary}</p>
                  </div>
                  <div>
                    <h5 className="text-sm font-semibold text-white mb-2">Technical Description</h5>
                    <p className="text-gray-300 text-sm">{selectedVulnerability.ai_analysis.vulnerability_analysis.technical_description}</p>
                  </div>
                </div>
              </div>

              {/* Impact */}
              <div className="bg-red-500/10 border border-red-500/20 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-red-300 mb-4">Impact Assessment</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-400">Confidentiality</p>
                    <p className="text-gray-300">{selectedVulnerability.ai_analysis.vulnerability_analysis.impact_assessment.confidentiality}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Integrity</p>
                    <p className="text-gray-300">{selectedVulnerability.ai_analysis.vulnerability_analysis.impact_assessment.integrity}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Availability</p>
                    <p className="text-gray-300">{selectedVulnerability.ai_analysis.vulnerability_analysis.impact_assessment.availability}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Business Impact</p>
                    <p className="text-gray-300">{selectedVulnerability.ai_analysis.vulnerability_analysis.impact_assessment.business_impact}</p>
                  </div>
                </div>
              </div>

              {/* Remediation */}
              <div className="bg-green-500/10 border border-green-500/20 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-green-300 mb-4">Remediation Plan</h4>
                <div className="space-y-4">
                  <div>
                    <h5 className="text-sm font-semibold text-white mb-2">Immediate Actions</h5>
                    <ul className="text-gray-300 text-sm space-y-1">
                      {selectedVulnerability.ai_analysis.vulnerability_analysis.remediation.immediate_actions.map((action, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <span className="text-green-400 mt-1">•</span>
                          <span>{action}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h5 className="text-sm font-semibold text-white mb-2">Long-term Solutions</h5>
                    <ul className="text-gray-300 text-sm space-y-1">
                      {selectedVulnerability.ai_analysis.vulnerability_analysis.remediation.long_term_solutions.map((solution, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <span className="text-blue-400 mt-1">•</span>
                          <span>{solution}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-gray-400">Priority</p>
                      <span className={`px-2 py-1 rounded text-xs ${
                        analyticsUtils.formatPriority(selectedVulnerability.ai_analysis.vulnerability_analysis.remediation.priority).bgColor
                      } ${analyticsUtils.formatPriority(selectedVulnerability.ai_analysis.vulnerability_analysis.remediation.priority).color}`}>
                        {analyticsUtils.formatPriority(selectedVulnerability.ai_analysis.vulnerability_analysis.remediation.priority).text}
                      </span>
                    </div>
                    <div>
                      <p className="text-gray-400">Estimated Effort</p>
                      <p className="text-gray-300">{selectedVulnerability.ai_analysis.vulnerability_analysis.remediation.estimated_effort}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Responsibilities */}
              <div className="bg-purple-500/10 border border-purple-500/20 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-purple-300 mb-4">Responsibility Assignment</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-gray-400">Primary Responsible</p>
                    <p className="text-gray-300">{selectedVulnerability.ai_analysis.vulnerability_analysis.responsibility_assignment.primary_responsible}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Secondary Responsible</p>
                    <p className="text-gray-300">{selectedVulnerability.ai_analysis.vulnerability_analysis.responsibility_assignment.secondary_responsible || 'N/A'}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Escalation Contact</p>
                    <p className="text-gray-300">{selectedVulnerability.ai_analysis.vulnerability_analysis.responsibility_assignment.escalation_contact}</p>
                  </div>
                  <div>
                    <p className="text-gray-400">Required Skills</p>
                    <p className="text-gray-300">{selectedVulnerability.ai_analysis.vulnerability_analysis.responsibility_assignment.required_skills.join(', ')}</p>
                  </div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    if (selectedVulnerability) {
                      downloadSingleVulnerabilityMarkdown(selectedVulnerability);
                      showToast({
                        type: 'success',
                        message: 'Vulnerability exported to Markdown!',
                        duration: 3000
                      });
                    }
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 text-white rounded-xl transition-all duration-200"
                >
                  <FileText className="w-4 h-4" />
                  <span>Export MD</span>
                </button>
                <button
                  onClick={() => {
                    const dataStr = JSON.stringify(selectedVulnerability, null, 2);
                    const dataBlob = new Blob([dataStr], { type: 'application/json' });
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `vulnerability_${selectedVulnerability.basic_info.scan_id}.json`;
                    link.click();
                    URL.revokeObjectURL(url);
                    showToast({
                      type: 'success',
                      message: 'Vulnerability exported to JSON!',
                      duration: 3000
                    });
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-xl transition-all duration-200"
                >
                  <Download className="w-4 h-4" />
                  <span>Export JSON</span>
                </button>
                <button
                  onClick={() => {
                    setShowVulnerabilityModal(false);
                    setSelectedVulnerability(null);
                  }}
                  className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-xl transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Toast Notifications */}
      <ToastContainer toasts={toasts} onClose={hideToast} />

      {/* Async Error Display */}
      {asyncError && (
        <div className="fixed bottom-4 left-4 z-50 max-w-md p-4 bg-red-900/90 border border-red-500/50 rounded-xl shadow-2xl backdrop-blur-xl">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
            <div className="flex-1">
              <h4 className="font-semibold text-red-100">Async Operation Failed</h4>
              <p className="text-sm text-red-200 mt-1">{asyncError.message}</p>
            </div>
            <button
              onClick={clearError}
              className="text-red-400 hover:text-red-200 transition-colors"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}
    </div>

  );
};

export default Analytics;
